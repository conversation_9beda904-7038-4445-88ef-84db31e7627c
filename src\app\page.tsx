'use client';

import { useState, useEffect } from 'react';
import { Upload, FileText, Image as ImageIcon, Download, Loader2 } from 'lucide-react';
import { ocrService, readTextFile } from '@/utils/ocr';
import { imageProcessor } from '@/utils/imageProcessor';
import { saveAs } from 'file-saver';

export default function Home() {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [textFile, setTextFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [originalImageUrl, setOriginalImageUrl] = useState<string>('');
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');
  const [dragOver, setDragOver] = useState<'image' | 'text' | null>(null);
  const [error, setError] = useState<string>('');
  const [processingStatus, setProcessingStatus] = useState<string>('');
  const [processedBlob, setProcessedBlob] = useState<Blob | null>(null);
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [textFileContent, setTextFileContent] = useState<string[]>([]);

  // File validation
  const validateImageFile = (file: File): boolean => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!validTypes.includes(file.type)) {
      setError('Please upload a valid image file (JPG, PNG, GIF, WebP, BMP)');
      return false;
    }

    if (file.size > maxSize) {
      setError('Image file size must be less than 10MB');
      return false;
    }

    return true;
  };

  const validateTextFile = (file: File): boolean => {
    const validTypes = ['text/plain'];
    const maxSize = 1024 * 1024; // 1MB

    if (!validTypes.includes(file.type) && !file.name.endsWith('.txt')) {
      setError('Please upload a valid text file (.txt)');
      return false;
    }

    if (file.size > maxSize) {
      setError('Text file size must be less than 1MB');
      return false;
    }

    return true;
  };

  // Handle image file selection
  const handleImageFile = (file: File) => {
    setError('');
    setSuccessMessage('');
    // Clear previous results
    setProcessedImageUrl('');
    setProcessedBlob(null);

    if (validateImageFile(file)) {
      setImageFile(file);
      setOriginalImageUrl(URL.createObjectURL(file));
    }
  };

  // Handle text file selection
  const handleTextFile = async (file: File) => {
    setError('');
    setSuccessMessage('');
    if (validateTextFile(file)) {
      setTextFile(file);
      try {
        const content = await readTextFile(file);
        setTextFileContent(content);
        setSuccessMessage(`Loaded ${content.length} text entries to remove`);
      } catch (error) {
        setError('Failed to read text file content');
      }
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent, type: 'image' | 'text') => {
    e.preventDefault();
    setDragOver(type);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(null);
  };

  const handleDrop = (e: React.DragEvent, type: 'image' | 'text') => {
    e.preventDefault();
    setDragOver(null);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      const file = files[0];
      if (type === 'image') {
        handleImageFile(file);
      } else {
        handleTextFile(file);
      }
    }
  };

  // Main processing function
  const processImage = async () => {
    if (!imageFile || !textFile) {
      setError('Please upload both an image and text file');
      return;
    }

    setIsProcessing(true);
    setError('');
    setProcessingStatus('Initializing OCR...');

    try {
      // Initialize OCR service
      await ocrService.initialize();

      // Read text file content
      setProcessingStatus('Reading text file...');
      const targetTexts = await readTextFile(textFile);

      if (targetTexts.length === 0) {
        throw new Error('Text file is empty or contains no valid text');
      }

      // Perform OCR on the image
      setProcessingStatus('Analyzing image text...');
      const ocrResult = await ocrService.recognizeText(imageFile);

      // Find matching text
      setProcessingStatus('Finding text matches...');
      const textMatches = ocrService.findTextMatches(ocrResult, targetTexts);

      console.log(`OCR detected ${ocrResult.words.length} words, found ${textMatches.length} matches`);

      if (textMatches.length === 0) {
        setError(`No matching text found in the image. OCR detected: "${ocrResult.text.substring(0, 100)}${ocrResult.text.length > 100 ? '...' : ''}"`);
        return;
      }

      // Process image to remove text
      setProcessingStatus('Removing text from image...');
      const processedBlob = await imageProcessor.removeTextFromImage(
        imageFile,
        textMatches,
        { fillColor: '#FFFFFF', padding: 3 }
      );

      // Create URL for processed image
      const processedUrl = URL.createObjectURL(processedBlob);
      setProcessedImageUrl(processedUrl);
      setProcessedBlob(processedBlob);

      setSuccessMessage(`Successfully removed ${textMatches.length} text match${textMatches.length > 1 ? 'es' : ''} from the image!`);
      setProcessingStatus('');

    } catch (error) {
      console.error('Processing error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred during processing');
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
    }
  };

  // Download processed image
  const downloadProcessedImage = () => {
    if (processedBlob && imageFile) {
      const fileName = `processed_${imageFile.name.replace(/\.[^/.]+$/, '')}.png`;
      saveAs(processedBlob, fileName);
    }
  };

  // Cleanup function
  useEffect(() => {
    return () => {
      // Cleanup object URLs to prevent memory leaks
      if (originalImageUrl) {
        URL.revokeObjectURL(originalImageUrl);
      }
      if (processedImageUrl) {
        URL.revokeObjectURL(processedImageUrl);
      }
      // Terminate OCR worker
      ocrService.terminate();
    };
  }, [originalImageUrl, processedImageUrl]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <ImageIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Text Remover
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Remove specific text from images using OCR
                </p>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Instructions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            How to use this tool:
          </h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-400">
            <li>Upload an image file (JPG, PNG, GIF, etc.)</li>
            <li>Upload a text file containing words/phrases to remove</li>
            <li>Click "Process Image" to detect and remove matching text</li>
            <li>Download the processed image with text removed</li>
          </ol>
        </div>

        {/* File Upload Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Image Upload */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <ImageIcon className="h-5 w-5 mr-2" />
              Upload Image
            </h3>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragOver === 'image'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
              }`}
              onDragOver={(e) => handleDragOver(e, 'image')}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, 'image')}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                Drag and drop your image here, or click to browse
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                Supports JPG, PNG, GIF, WebP, BMP (max 10MB)
              </p>
              <input
                type="file"
                accept="image/*"
                className="hidden"
                id="image-upload"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleImageFile(file);
                  }
                }}
              />
              <label
                htmlFor="image-upload"
                className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors"
              >
                Choose Image
              </label>
            </div>
            {imageFile && (
              <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                <p className="text-sm text-green-800 dark:text-green-400">
                  ✓ {imageFile.name} ({(imageFile.size / 1024 / 1024).toFixed(2)} MB)
                </p>
              </div>
            )}
          </div>

          {/* Text File Upload */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Upload Text File
            </h3>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragOver === 'text'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
              }`}
              onDragOver={(e) => handleDragOver(e, 'text')}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, 'text')}
            >
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                Upload a text file with words to remove
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                One word or phrase per line (max 1MB)
              </p>
              <input
                type="file"
                accept=".txt,.text"
                className="hidden"
                id="text-upload"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleTextFile(file);
                  }
                }}
              />
              <label
                htmlFor="text-upload"
                className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors"
              >
                Choose Text File
              </label>
            </div>
            {textFile && (
              <div className="mt-4 space-y-2">
                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                  <p className="text-sm text-green-800 dark:text-green-400">
                    ✓ {textFile.name} ({(textFile.size / 1024).toFixed(2)} KB)
                  </p>
                </div>
                {textFileContent.length > 0 && (
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      Text to remove ({textFileContent.length} entries):
                    </p>
                    <div className="text-xs text-gray-800 dark:text-gray-200 max-h-20 overflow-y-auto">
                      {textFileContent.slice(0, 10).map((text, index) => (
                        <div key={index} className="truncate">• {text}</div>
                      ))}
                      {textFileContent.length > 10 && (
                        <div className="text-gray-500 italic">... and {textFileContent.length - 10} more</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8">
            <p className="text-red-800 dark:text-red-400 text-sm">
              ⚠️ {error}
            </p>
          </div>
        )}

        {/* Success Display */}
        {successMessage && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-8">
            <p className="text-green-800 dark:text-green-400 text-sm">
              ✅ {successMessage}
            </p>
          </div>
        )}

        {/* Process Button */}
        <div className="text-center mb-8">
          <button
            disabled={!imageFile || !textFile || isProcessing}
            onClick={processImage}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                {processingStatus || 'Processing...'}
              </>
            ) : (
              'Process Image'
            )}
          </button>
        </div>

        {/* Results Section */}
        {(originalImageUrl || processedImageUrl) && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
              Results
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Original Image */}
              {originalImageUrl && (
                <div>
                  <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Original Image
                  </h4>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <img
                      src={originalImageUrl}
                      alt="Original"
                      className="w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                </div>
              )}

              {/* Processed Image */}
              {processedImageUrl && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">
                      Processed Image
                    </h4>
                    <button
                      onClick={downloadProcessedImage}
                      className="inline-flex items-center px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </button>
                  </div>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <img
                      src={processedImageUrl}
                      alt="Processed"
                      className="w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
