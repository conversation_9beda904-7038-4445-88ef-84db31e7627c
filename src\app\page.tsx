'use client';

import { useState, useEffect } from 'react';
import { Upload, FileText, Image as ImageIcon, Download, Loader2, <PERSON><PERSON><PERSON>, Zap, Eye, RotateCcw } from 'lucide-react';
import { ocrService, readTextFile } from '@/utils/ocr';
import { getImageProcessor } from '@/utils/imageProcessor';
import { saveAs } from 'file-saver';

export default function Home() {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [textFile, setTextFile] = useState<File | null>(null);
  const [batchMode, setBatchMode] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [originalImageUrl, setOriginalImageUrl] = useState<string>('');
  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');
  const [dragOver, setDragOver] = useState<'image' | 'text' | null>(null);
  const [error, setError] = useState<string>('');
  const [processingStatus, setProcessingStatus] = useState<string>('');
  const [processedBlob, setProcessedBlob] = useState<Blob | null>(null);
  const [successMessage, setSuccessMessage] = useState<string>('');
  const [textFileContent, setTextFileContent] = useState<string[]>([]);
  const [processingOptions, setProcessingOptions] = useState({
    fillColor: '#FFFFFF',
    padding: 3,
    useSmartFill: true,
    blurRadius: 1
  });
  const [ocrLanguage, setOcrLanguage] = useState('eng+chi_sim');
  const [processingProgress, setProcessingProgress] = useState(0);
  const [processingStats, setProcessingStats] = useState({
    totalWords: 0,
    matchedWords: 0,
    processingTime: 0
  });

  // Load saved settings on component mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('textRemoverSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        setProcessingOptions(prev => ({ ...prev, ...settings.processingOptions }));
        setOcrLanguage(settings.ocrLanguage || 'eng+chi_sim');
      } catch (error) {
        console.error('Failed to load saved settings:', error);
      }
    }
  }, []);

  // Save settings when they change
  useEffect(() => {
    const settings = {
      processingOptions,
      ocrLanguage
    };
    localStorage.setItem('textRemoverSettings', JSON.stringify(settings));
  }, [processingOptions, ocrLanguage]);

  // File validation
  const validateImageFile = (file: File): boolean => {
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];
    const maxSize = 10 * 1024 * 1024; // 10MB

    if (!validTypes.includes(file.type)) {
      setError('Please upload a valid image file (JPG, PNG, GIF, WebP, BMP)');
      return false;
    }

    if (file.size > maxSize) {
      setError('Image file size must be less than 10MB');
      return false;
    }

    return true;
  };

  const validateTextFile = (file: File): boolean => {
    const validTypes = ['text/plain'];
    const maxSize = 1024 * 1024; // 1MB

    if (!validTypes.includes(file.type) && !file.name.endsWith('.txt')) {
      setError('Please upload a valid text file (.txt)');
      return false;
    }

    if (file.size > maxSize) {
      setError('Text file size must be less than 1MB');
      return false;
    }

    return true;
  };

  // Handle image file selection
  const handleImageFile = (file: File) => {
    setError('');
    setSuccessMessage('');
    // Clear previous results
    setProcessedImageUrl('');
    setProcessedBlob(null);

    if (validateImageFile(file)) {
      setImageFile(file);
      setOriginalImageUrl(URL.createObjectURL(file));
    }
  };

  // Handle multiple image files selection
  const handleImageFiles = (files: FileList) => {
    setError('');
    setSuccessMessage('');
    const validFiles: File[] = [];

    Array.from(files).forEach(file => {
      if (validateImageFile(file)) {
        validFiles.push(file);
      }
    });

    if (validFiles.length > 0) {
      setImageFiles(validFiles);
      setSuccessMessage(`已选择 ${validFiles.length} 个图片文件进行批量处理`);
    }
  };

  // Handle text file selection
  const handleTextFile = async (file: File) => {
    setError('');
    setSuccessMessage('');
    if (validateTextFile(file)) {
      setTextFile(file);
      try {
        const content = await readTextFile(file);
        setTextFileContent(content);
        setSuccessMessage(`Loaded ${content.length} text entries to remove`);
      } catch (error) {
        setError('Failed to read text file content');
      }
    }
  };

  // Drag and drop handlers
  const handleDragOver = (e: React.DragEvent, type: 'image' | 'text') => {
    e.preventDefault();
    setDragOver(type);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(null);
  };

  const handleDrop = (e: React.DragEvent, type: 'image' | 'text') => {
    e.preventDefault();
    setDragOver(null);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      if (type === 'image') {
        if (batchMode && files.length > 1) {
          const fileList = new DataTransfer();
          files.forEach(file => fileList.items.add(file));
          handleImageFiles(fileList.files);
        } else {
          handleImageFile(files[0]);
        }
      } else {
        handleTextFile(files[0]);
      }
    }
  };

  // Main processing function
  const processImage = async () => {
    if (!imageFile || !textFile) {
      setError('Please upload both an image and text file');
      return;
    }

    setIsProcessing(true);
    setError('');
    setProcessingStatus('Initializing OCR...');
    setProcessingProgress(0);
    const startTime = Date.now();

    try {
      // Initialize OCR service
      setProcessingProgress(10);
      await ocrService.initialize(ocrLanguage);

      // Read text file content
      setProcessingStatus('Reading text file...');
      setProcessingProgress(20);
      const targetTexts = await readTextFile(textFile);

      if (targetTexts.length === 0) {
        throw new Error('Text file is empty or contains no valid text');
      }

      // Perform OCR on the image
      setProcessingStatus('Analyzing image text...');
      setProcessingProgress(40);
      const ocrResult = await ocrService.recognizeText(imageFile);

      // Find matching text
      setProcessingStatus('Finding text matches...');
      setProcessingProgress(60);
      const textMatches = ocrService.findTextMatches(ocrResult, targetTexts);

      console.log(`OCR detected ${ocrResult.words.length} words, found ${textMatches.length} matches`);

      // Update stats
      setProcessingStats({
        totalWords: ocrResult.words.length,
        matchedWords: textMatches.length,
        processingTime: Date.now() - startTime
      });

      if (textMatches.length === 0) {
        setError(`No matching text found in the image. OCR detected: "${ocrResult.text.substring(0, 100)}${ocrResult.text.length > 100 ? '...' : ''}"`);
        return;
      }

      // Process image to remove text
      setProcessingStatus('Removing text from image...');
      setProcessingProgress(80);
      const imageProcessor = getImageProcessor();
      const processedBlob = await imageProcessor.removeTextFromImage(
        imageFile,
        textMatches,
        {
          fillColor: processingOptions.useSmartFill ? undefined : processingOptions.fillColor,
          padding: processingOptions.padding,
          blurRadius: processingOptions.blurRadius
        }
      );

      // Create URL for processed image
      setProcessingProgress(95);
      const processedUrl = URL.createObjectURL(processedBlob);
      setProcessedImageUrl(processedUrl);
      setProcessedBlob(processedBlob);

      setProcessingProgress(100);
      const finalTime = Date.now() - startTime;
      setProcessingStats(prev => ({ ...prev, processingTime: finalTime }));
      setSuccessMessage(`Successfully removed ${textMatches.length} text match${textMatches.length > 1 ? 'es' : ''} from the image in ${(finalTime/1000).toFixed(1)}s!`);
      setProcessingStatus('');

    } catch (error) {
      console.error('Processing error:', error);
      setError(error instanceof Error ? error.message : 'An error occurred during processing');
    } finally {
      setIsProcessing(false);
      setProcessingStatus('');
    }
  };

  // Download processed image
  const downloadProcessedImage = () => {
    if (processedBlob && imageFile) {
      const fileName = `processed_${imageFile.name.replace(/\.[^/.]+$/, '')}.png`;
      saveAs(processedBlob, fileName);
    }
  };

  // Reset all data
  const resetAll = () => {
    setImageFile(null);
    setImageFiles([]);
    setTextFile(null);
    setOriginalImageUrl('');
    setProcessedImageUrl('');
    setProcessedBlob(null);
    setError('');
    setSuccessMessage('');
    setTextFileContent([]);
    setProcessingProgress(0);
    setProcessingStats({ totalWords: 0, matchedWords: 0, processingTime: 0 });
    setBatchMode(false);
  };

  // Cleanup function
  useEffect(() => {
    return () => {
      // Cleanup object URLs to prevent memory leaks
      if (originalImageUrl) {
        URL.revokeObjectURL(originalImageUrl);
      }
      if (processedImageUrl) {
        URL.revokeObjectURL(processedImageUrl);
      }
      // Terminate OCR worker
      ocrService.terminate();
    };
  }, [originalImageUrl, processedImageUrl]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl/Cmd + Enter to process image
      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        if (imageFile && textFile && !isProcessing) {
          e.preventDefault();
          processImage();
        }
      }

      // Ctrl/Cmd + D to download processed image
      if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
        if (processedBlob) {
          e.preventDefault();
          downloadProcessedImage();
        }
      }

      // Escape to clear error messages
      if (e.key === 'Escape') {
        setError('');
        setSuccessMessage('');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [imageFile, textFile, isProcessing, processedBlob]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-600 p-2 rounded-lg">
                <ImageIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Text Remover
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  使用OCR技术从图片中移除指定文字
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={resetAll}
                className="inline-flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                title="重置所有数据"
              >
                <RotateCcw className="h-4 w-4 mr-1" />
                重置
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Instructions */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            使用说明:
          </h2>
          <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-400 mb-4">
            <li>上传图片文件 (JPG, PNG, GIF 等格式)</li>
            <li>上传包含要移除文字的文本文件</li>
            <li>点击"Process Image"开始检测和移除匹配的文字</li>
            <li>下载处理后的图片</li>
          </ol>

          <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
              快捷键:
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-500 dark:text-gray-400">
              <div>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+Enter</kbd>
                <span className="ml-2">处理图片</span>
              </div>
              <div>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+D</kbd>
                <span className="ml-2">下载结果</span>
              </div>
              <div>
                <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded">Esc</kbd>
                <span className="ml-2">清除消息</span>
              </div>
            </div>
          </div>
        </div>

        {/* File Upload Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Image Upload */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white flex items-center">
                <ImageIcon className="h-5 w-5 mr-2" />
                Upload Image
              </h3>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={batchMode}
                  onChange={(e) => setBatchMode(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700 dark:text-gray-300">
                  批量模式
                </span>
              </label>
            </div>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragOver === 'image'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
              }`}
              onDragOver={(e) => handleDragOver(e, 'image')}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, 'image')}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                Drag and drop your image here, or click to browse
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                Supports JPG, PNG, GIF, WebP, BMP (max 10MB)
              </p>
              <input
                type="file"
                accept="image/*"
                multiple={batchMode}
                className="hidden"
                id="image-upload"
                onChange={(e) => {
                  if (batchMode && e.target.files) {
                    handleImageFiles(e.target.files);
                  } else {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleImageFile(file);
                    }
                  }
                }}
              />
              <label
                htmlFor="image-upload"
                className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors"
              >
                Choose Image
              </label>
            </div>
            {imageFile && !batchMode && (
              <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                <p className="text-sm text-green-800 dark:text-green-400">
                  ✓ {imageFile.name} ({(imageFile.size / 1024 / 1024).toFixed(2)} MB)
                </p>
              </div>
            )}

            {batchMode && imageFiles.length > 0 && (
              <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                <p className="text-sm text-green-800 dark:text-green-400 mb-2">
                  ✓ 已选择 {imageFiles.length} 个图片文件
                </p>
                <div className="max-h-32 overflow-y-auto space-y-1">
                  {imageFiles.map((file, index) => (
                    <div key={index} className="text-xs text-green-700 dark:text-green-300 truncate">
                      • {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Text File Upload */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Upload Text File
            </h3>
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragOver === 'text'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'
              }`}
              onDragOver={(e) => handleDragOver(e, 'text')}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, 'text')}
            >
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-400 mb-2">
                Upload a text file with words to remove
              </p>
              <p className="text-sm text-gray-500 dark:text-gray-500">
                One word or phrase per line (max 1MB)
              </p>
              <input
                type="file"
                accept=".txt,.text"
                className="hidden"
                id="text-upload"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleTextFile(file);
                  }
                }}
              />
              <label
                htmlFor="text-upload"
                className="mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors"
              >
                Choose Text File
              </label>
            </div>
            {textFile && (
              <div className="mt-4 space-y-2">
                <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                  <p className="text-sm text-green-800 dark:text-green-400">
                    ✓ {textFile.name} ({(textFile.size / 1024).toFixed(2)} KB)
                  </p>
                </div>
                {textFileContent.length > 0 && (
                  <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      Text to remove ({textFileContent.length} entries):
                    </p>
                    <div className="text-xs text-gray-800 dark:text-gray-200 max-h-20 overflow-y-auto">
                      {textFileContent.slice(0, 10).map((text, index) => (
                        <div key={index} className="truncate">• {text}</div>
                      ))}
                      {textFileContent.length > 10 && (
                        <div className="text-gray-500 italic">... and {textFileContent.length - 10} more</div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8">
            <p className="text-red-800 dark:text-red-400 text-sm">
              ⚠️ {error}
            </p>
          </div>
        )}

        {/* Success Display */}
        {successMessage && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-8">
            <p className="text-green-800 dark:text-green-400 text-sm">
              ✅ {successMessage}
            </p>
          </div>
        )}

        {/* Processing Options */}
        {imageFile && textFile && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              处理选项
            </h3>

            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                OCR识别语言
              </label>
              <select
                value={ocrLanguage}
                onChange={(e) => setOcrLanguage(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              >
                <option value="eng">English (英语)</option>
                <option value="chi_sim">简体中文</option>
                <option value="chi_tra">繁体中文</option>
                <option value="eng+chi_sim">English + 简体中文</option>
                <option value="eng+chi_tra">English + 繁体中文</option>
                <option value="jpn">日本語</option>
                <option value="kor">한국어</option>
                <option value="fra">Français</option>
                <option value="deu">Deutsch</option>
                <option value="spa">Español</option>
              </select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  填充颜色
                </label>
                <div className="flex items-center space-x-3">
                  <input
                    type="color"
                    value={processingOptions.fillColor}
                    onChange={(e) => setProcessingOptions(prev => ({ ...prev, fillColor: e.target.value }))}
                    className="w-12 h-8 rounded border border-gray-300 dark:border-gray-600"
                  />
                  <input
                    type="text"
                    value={processingOptions.fillColor}
                    onChange={(e) => setProcessingOptions(prev => ({ ...prev, fillColor: e.target.value }))}
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    placeholder="#FFFFFF"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  边距填充: {processingOptions.padding}px
                </label>
                <input
                  type="range"
                  min="0"
                  max="10"
                  value={processingOptions.padding}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, padding: parseInt(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={processingOptions.useSmartFill}
                    onChange={(e) => setProcessingOptions(prev => ({ ...prev, useSmartFill: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    使用智能填充
                  </span>
                </label>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  根据周围像素智能选择填充颜色
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  模糊半径: {processingOptions.blurRadius}px
                </label>
                <input
                  type="range"
                  min="0"
                  max="5"
                  value={processingOptions.blurRadius}
                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, blurRadius: parseInt(e.target.value) }))}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
              </div>
            </div>
          </div>
        )}

        {/* Process Button */}
        <div className="text-center mb-8">
          <button
            disabled={!imageFile || !textFile || isProcessing}
            onClick={processImage}
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                {processingStatus || 'Processing...'}
              </>
            ) : (
              'Process Image'
            )}
          </button>

          {/* Progress Bar */}
          {isProcessing && (
            <div className="mt-4 max-w-md mx-auto">
              <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                <span>处理进度</span>
                <span>{processingProgress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${processingProgress}%` }}
                ></div>
              </div>
            </div>
          )}

          {/* Processing Stats */}
          {processingStats.totalWords > 0 && (
            <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex justify-center space-x-6">
                <span>检测到 {processingStats.totalWords} 个词</span>
                <span>匹配 {processingStats.matchedWords} 个</span>
                <span>耗时 {(processingStats.processingTime/1000).toFixed(1)}s</span>
              </div>
            </div>
          )}
        </div>

        {/* Results Section */}
        {(originalImageUrl || processedImageUrl) && (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-6">
              Results
            </h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Original Image */}
              {originalImageUrl && (
                <div>
                  <h4 className="text-md font-medium text-gray-700 dark:text-gray-300 mb-3">
                    Original Image
                  </h4>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <img
                      src={originalImageUrl}
                      alt="Original"
                      className="w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                </div>
              )}

              {/* Processed Image */}
              {processedImageUrl && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-md font-medium text-gray-700 dark:text-gray-300">
                      Processed Image
                    </h4>
                    <button
                      onClick={downloadProcessedImage}
                      className="inline-flex items-center px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors"
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </button>
                  </div>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <img
                      src={processedImageUrl}
                      alt="Processed"
                      className="w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
