# Text Remover - OCR-Based Text Removal Tool

A modern web application that uses Optical Character Recognition (OCR) to detect and remove specific text from images. Built with Next.js, TypeScript, and Tesseract.js.

## Features

- **Drag & Drop Interface**: Easy file upload for both images and text files
- **OCR Text Detection**: Automatically detects text in images using Tesseract.js
- **Smart Text Matching**: Compares detected text against your removal list
- **Intelligent Text Removal**: Removes matched text with context-aware filling
- **Multiple Image Formats**: Supports JPG, PNG, GIF, WebP, and BMP
- **Real-time Preview**: Side-by-side comparison of original and processed images
- **Download Results**: Save processed images as PNG files
- **Responsive Design**: Works on desktop and mobile devices
- **Dark Mode Support**: Automatic dark/light theme detection

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd delete-word
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## How to Use

1. **Upload an Image**: Drag and drop or click to select an image file (max 10MB)
2. **Upload Text File**: Create a text file with words/phrases to remove (one per line, max 1MB)
3. **Process Image**: Click "Process Image" to start OCR analysis and text removal
4. **Download Result**: Save the processed image with text removed

### Example Text File Format

Create a `.txt` file with words or phrases to remove:

```
WATERMARK
COPYRIGHT
CONFIDENTIAL
SAMPLE TEXT
DRAFT
```

## Technical Details

### Architecture

- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **OCR Engine**: Tesseract.js for client-side text recognition
- **Image Processing**: HTML5 Canvas for text removal and image manipulation
- **File Handling**: Browser-based file processing (no server required)

### Key Components

- `src/app/page.tsx` - Main application interface
- `src/utils/ocr.ts` - OCR service and text matching logic
- `src/utils/imageProcessor.ts` - Image processing and text removal

### Text Removal Algorithm

1. **OCR Analysis**: Extract text and bounding boxes from the image
2. **Text Matching**: Compare detected text with removal list
3. **Context-Aware Removal**:
   - Sample surrounding pixels for color matching
   - Apply gradient fills for natural appearance
   - Use intelligent padding around text areas

## Limitations

- OCR accuracy depends on image quality and text clarity
- Works best with clear, high-contrast text
- Processing time varies with image size and complexity
- Client-side processing may be slower on older devices

## Browser Compatibility

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Troubleshooting

### Common Issues

**OCR not detecting text:**
- Ensure image has clear, readable text
- Try images with higher contrast
- Check that text is not too small or blurry

**Processing takes too long:**
- Reduce image size before upload
- Use images with less complex backgrounds
- Close other browser tabs to free up memory

**Text removal looks unnatural:**
- The algorithm works best on simple backgrounds
- Complex patterns may not fill perfectly
- Consider manual editing for critical applications

## Development

### Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Main application
│   └── globals.css         # Global styles
├── utils/
│   ├── ocr.ts              # OCR service
│   └── imageProcessor.ts   # Image processing
└── ...
```

### Building for Production

```bash
npm run build
npm start
```

### Testing

The application includes comprehensive error handling and user feedback. Test with various image types and text files to ensure reliability.
