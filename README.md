# Text Remover - OCR-Based Text Removal Tool

A modern web application that uses Optical Character Recognition (OCR) to detect and remove specific text from images. Built with Next.js, TypeScript, and Tesseract.js.

## Features

### 🎯 核心功能
- **拖拽上传界面**: 支持图片和文本文件的拖拽上传
- **百度OCR识别**: 使用百度智能云OCR API，识别精度更高，特别适合中文识别
- **智能文字匹配**: 将检测到的文字与移除列表进行比较，支持模糊匹配
- **智能文字移除**: 使用上下文感知填充技术移除匹配的文字
- **多种图片格式**: 支持JPG, PNG, GIF, WebP, BMP等格式（最大4MB）
- **实时预览**: 原图与处理后图片的并排对比
- **结果下载**: 将处理后的图片保存为PNG格式

### 🚀 高级功能
- **百度OCR引擎**: 自动识别中英文混合文字，识别精度高达95%以上
- **批量处理**: 一次性处理多个图片文件
- **自定义处理选项**:
  - 自定义填充颜色
  - 可调节边距填充
  - 智能填充模式
  - 模糊半径控制
- **模糊匹配**: 处理OCR可能的识别错误，提高匹配准确率
- **进度跟踪**: 实时显示处理进度和统计信息
- **快捷键支持**:
  - `Ctrl+Enter`: 开始处理
  - `Ctrl+D`: 下载结果
  - `Esc`: 清除消息
- **设置保存**: 自动保存用户偏好设置
- **响应式设计**: 适配桌面和移动设备
- **深色模式**: 自动检测系统主题

## Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd delete-word
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Configure Baidu OCR API:
```bash
# Copy the environment template
cp .env.local.example .env.local

# Edit .env.local and add your Baidu OCR credentials
BAIDU_APP_ID=your_app_id_here
BAIDU_API_KEY=your_api_key_here
BAIDU_SECRET_KEY=your_secret_key_here
```

> 📋 详细的百度OCR配置说明请查看 [BAIDU_OCR_SETUP.md](./BAIDU_OCR_SETUP.md)

4. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## How to Use

### 基础使用流程

1. **上传图片**: 拖拽或点击选择图片文件 (最大10MB)
2. **上传文本文件**: 创建包含要移除文字的文本文件 (每行一个词/短语，最大1MB)
3. **选择语言**: 根据图片内容选择合适的OCR识别语言
4. **调整选项**: 根据需要调整处理选项（填充颜色、边距等）
5. **处理图片**: 点击"Process Image"开始OCR分析和文字移除
6. **下载结果**: 保存处理后的图片

### 高级功能

- **批量模式**: 勾选"批量模式"可以一次处理多个图片
- **智能填充**: 启用后会根据周围像素智能选择填充颜色
- **自定义颜色**: 手动指定填充颜色（如白色 #FFFFFF）
- **边距控制**: 调整文字周围的填充边距
- **模糊效果**: 添加轻微模糊使填充更自然

### 示例文本文件格式

创建一个 `.txt` 文件，包含要移除的文字（支持中英文混合）:

```
WATERMARK
COPYRIGHT
CONFIDENTIAL
SAMPLE TEXT
DRAFT
水印
版权
机密
草稿
测试
示例
仅供参考
FOR REFERENCE ONLY
```

## Technical Details

### Architecture

- **Frontend**: Next.js 15 with TypeScript and Tailwind CSS
- **OCR Engine**: Tesseract.js for client-side text recognition
- **Image Processing**: HTML5 Canvas for text removal and image manipulation
- **File Handling**: Browser-based file processing (no server required)

### Key Components

- `src/app/page.tsx` - Main application interface
- `src/utils/ocr.ts` - OCR service and text matching logic
- `src/utils/imageProcessor.ts` - Image processing and text removal

### Text Removal Algorithm

1. **OCR Analysis**: Extract text and bounding boxes from the image
2. **Text Matching**: Compare detected text with removal list
3. **Context-Aware Removal**:
   - Sample surrounding pixels for color matching
   - Apply gradient fills for natural appearance
   - Use intelligent padding around text areas

## Limitations

- OCR accuracy depends on image quality and text clarity
- Works best with clear, high-contrast text
- Processing time varies with image size and complexity
- Client-side processing may be slower on older devices

## Browser Compatibility

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Troubleshooting

### Common Issues

**OCR not detecting text:**
- Ensure image has clear, readable text
- Try images with higher contrast
- Check that text is not too small or blurry

**Processing takes too long:**
- Reduce image size before upload
- Use images with less complex backgrounds
- Close other browser tabs to free up memory

**Text removal looks unnatural:**
- The algorithm works best on simple backgrounds
- Complex patterns may not fill perfectly
- Consider manual editing for critical applications

## Development

### Project Structure

```
src/
├── app/
│   ├── layout.tsx          # Root layout
│   ├── page.tsx            # Main application
│   └── globals.css         # Global styles
├── utils/
│   ├── ocr.ts              # OCR service
│   └── imageProcessor.ts   # Image processing
└── ...
```

### Building for Production

```bash
npm run build
npm start
```

### Testing

The application includes comprehensive error handling and user feedback. Test with various image types and text files to ensure reliability.
