'use strict';
/**
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file baseClient
 * <AUTHOR>
 */
const DevAuth = require("../auth/devAuth");
const DevAuthToken = require("../auth/devAuthToken");
/**
 * 无授权判断状态
 *
 * @const
 * @type {number}
 */
const AUTHTYPE_INIT = 0;
/**
 * 确定为云用户
 *
 * @const
 * @type {number}
 */
const AUTHTYPE_BCE = 1;
/**
 * 确定为开发者用户（手动输入token模式,以及token中包含了正确的scope）
 *
 * @const
 * @type {number}
 */
const AUTHTYPE_DEV = 2;
/**
 * 获取开发者token成功用户
 *
 * @const
 * @type {number}
 */
const AUTHTYPE_DEV_OR_BCE = 3;
/**
 * 初始状态
 *
 * @const
 * @type {number}
 */
const STATUS_INIT = 0;
/**
 * 获取开发者token中
 *
 * @const
 * @type {number}
 */
const STATUS_AUTHTYPE_REQESTING = 1;
/**
 * 获取开发者token成功，或者确定为云用户
 *
 * @const
 * @type {number}
 */
const STATUS_READY = 2;
/**
 * 非法ak，sk
 *
 * @const
 * @type {number}
 */
const STATUS_ERROR = -1;
/**
* BaseClient类
* 各具体接口类基类，处理鉴权逻辑等
*
* @constructor
* @param {string} appid appid.
* @param {string} ak The access key.
* @param {string} sk The security key.
*/
class BaseClient {
    constructor(appId, ak, sk, options) {
        this.appId = 0;
        this.appId = 0;
        this.ak = ak;
        this.sk = sk;
        this.options = options || {};
        this.authType = AUTHTYPE_INIT;
        this.status = STATUS_INIT;
        this.pms;
        this.devAccessToken = null;
        this.devAuth = new DevAuth(this.ak, this.sk);
        this.authTypeReq();
    }
    setAccessToken(token, expireTime) {
        let et = expireTime || DevAuthToken.DEFAULT_EXPIRE_DURATION;
        this.devAccessToken = new DevAuthToken(token, et, null);
        this.authType = AUTHTYPE_DEV;
        this.status = STATUS_READY;
    }
    authTypeReq() {
        // 请求access_token服务
        this.status = STATUS_AUTHTYPE_REQESTING;
        this.pms = this.devAuth.getToken().then(this.gotDevAuthSuccess.bind(this), this.gotDevAuthFail.bind(this));
        // 初始化client对象后立即发生的第一次异常，如果没有立即调用具体请求接口的话（必须有promise catch）
        // 将无法被捕获获取token的request网络异常，为了避免UnhandledPromiseRejectionWarning
        // 此处直接catch住,待代用具体接口时候再返回获取token时的异常，减少程序复杂度
        this.pms.catch(function (error) {
        }.bind(this));
        return this.pms;
    }
    gotDevAuthSuccess(token) {
        // 如果用户没有手动调用setAccessToken设置access_token
        if (this.authType !== AUTHTYPE_DEV) {
            this.devAccessToken = token;
            this.authType = AUTHTYPE_DEV_OR_BCE;
        }
        this.status = STATUS_READY;
    }
    gotDevAuthFail(err) {
        // 获取token时鉴权服务器返回失败信息
        if (err.errorType === DevAuth.EVENT_ERRTYPE_NORMAL) {
            // 可能是百度云的ak，sk
            this.authType = AUTHTYPE_BCE;
            this.status = STATUS_READY;
            return;
        }
        // 获取token时发生了网络错误
        // 或者是发生了服务器返回数据格式异常
        if (err.errorType === DevAuth.EVENT_ERRTYPE_NETWORK
            || err.errorType === DevAuth.EVENT_ERRTYPE_ILLEGAL_RESPONSE) {
            this.status = STATUS_ERROR;
            throw err;
        }
    }
    doRequest(requestInfo, httpClient) {
        // 如果获取token失败
        if (this.status === STATUS_ERROR) {
            this.authTypeReq();
        }
        // @ts-ignore
        return this.pms.then(function () {
            // 预检函数，返回是否token过期
            let isTokenExpired = this.preRequest(requestInfo);
            if (isTokenExpired === false) {
                // 鉴权方式确定，请求接口
                return httpClient.postWithInfo(requestInfo);
            }
            else {
                // 如果token过期了，说明是需要重新获取access_token
                // 待重新获取完后继续请求接口
                return this.pms.then(function () {
                    this.preRequest(requestInfo);
                    return httpClient.postWithInfo(requestInfo);
                }.bind(this));
            }
        }.bind(this));
    }
    checkDevPermission(requestInfo) {
        // 是否跳过这个检查（从speech.baidu.com创建的应用，调用语音接口需要跳过）
        if (this.options.isSkipScopeCheck === true) {
            return true;
        }
        // 检查是否拥有AI平台接口权限
        return this.devAccessToken.hasScope(requestInfo.scope);
    }
    preRequest(requestInfo) {
        // 获取access_token失败，使用百度云签名方式调用
        if (this.authType === AUTHTYPE_BCE) {
            requestInfo.makeBceOptions(this.ak, this.sk);
            return false;
        }
        // 获取access_token成功，或者调用setAccessToken设置的access_token
        if (this.authType === AUTHTYPE_DEV_OR_BCE || this.authType === AUTHTYPE_DEV) {
            // 拥有AI平台接口权限
            if (this.checkDevPermission(requestInfo) || this.authType === AUTHTYPE_DEV) {
                // 判断access_token是否过期
                if (!this.devAccessToken.isExpired()) {
                    requestInfo.makeDevOptions(this.devAccessToken);
                    return false;
                }
                // access_token过期重新获取
                this.authTypeReq();
                return true;
            }
            else {
                // 使用百度云签名方式访问调用
                requestInfo.makeBceOptions(this.ak, this.sk);
            }
        }
        return false;
    }
}
exports.default = BaseClient;
// @ts-ignore
Object.assign(BaseClient, exports);
module.exports = BaseClient;
//# sourceMappingURL=data:application/json;base64,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