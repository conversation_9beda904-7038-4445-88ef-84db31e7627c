/*istanbul ignore start*/'use strict';

exports.__esModule = true;
exports['default'] = /*istanbul ignore end*/Diff;
function Diff() {}

Diff.prototype = {
  /*istanbul ignore start*/ /*istanbul ignore end*/diff: function diff(oldString, newString) {
    /*istanbul ignore start*/var /*istanbul ignore end*/options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};

    var callback = options.callback;
    if (typeof options === 'function') {
      callback = options;
      options = {};
    }
    this.options = options;

    var self = this;

    function done(value) {
      if (callback) {
        setTimeout(function () {
          callback(undefined, value);
        }, 0);
        return true;
      } else {
        return value;
      }
    }

    // Allow subclasses to massage the input prior to running
    oldString = this.castInput(oldString);
    newString = this.castInput(newString);

    oldString = this.removeEmpty(this.tokenize(oldString));
    newString = this.removeEmpty(this.tokenize(newString));

    var newLen = newString.length,
        oldLen = oldString.length;
    var editLength = 1;
    var maxEditLength = newLen + oldLen;
    var bestPath = [{ newPos: -1, components: [] }];

    // Seed editLength = 0, i.e. the content starts with the same values
    var oldPos = this.extractCommon(bestPath[0], newString, oldString, 0);
    if (bestPath[0].newPos + 1 >= newLen && oldPos + 1 >= oldLen) {
      // Identity per the equality and tokenizer
      return done([{ value: this.join(newString), count: newString.length }]);
    }

    // Main worker method. checks all permutations of a given edit length for acceptance.
    function execEditLength() {
      for (var diagonalPath = -1 * editLength; diagonalPath <= editLength; diagonalPath += 2) {
        var basePath = /*istanbul ignore start*/void 0 /*istanbul ignore end*/;
        var addPath = bestPath[diagonalPath - 1],
            removePath = bestPath[diagonalPath + 1],
            _oldPos = (removePath ? removePath.newPos : 0) - diagonalPath;
        if (addPath) {
          // No one else is going to attempt to use this value, clear it
          bestPath[diagonalPath - 1] = undefined;
        }

        var canAdd = addPath && addPath.newPos + 1 < newLen,
            canRemove = removePath && 0 <= _oldPos && _oldPos < oldLen;
        if (!canAdd && !canRemove) {
          // If this path is a terminal then prune
          bestPath[diagonalPath] = undefined;
          continue;
        }

        // Select the diagonal that we want to branch from. We select the prior
        // path whose position in the new string is the farthest from the origin
        // and does not pass the bounds of the diff graph
        if (!canAdd || canRemove && addPath.newPos < removePath.newPos) {
          basePath = clonePath(removePath);
          self.pushComponent(basePath.components, undefined, true);
        } else {
          basePath = addPath; // No need to clone, we've pulled it from the list
          basePath.newPos++;
          self.pushComponent(basePath.components, true, undefined);
        }

        _oldPos = self.extractCommon(basePath, newString, oldString, diagonalPath);

        // If we have hit the end of both strings, then we are done
        if (basePath.newPos + 1 >= newLen && _oldPos + 1 >= oldLen) {
          return done(buildValues(self, basePath.components, newString, oldString, self.useLongestToken));
        } else {
          // Otherwise track this path as a potential candidate and continue.
          bestPath[diagonalPath] = basePath;
        }
      }

      editLength++;
    }

    // Performs the length of edit iteration. Is a bit fugly as this has to support the
    // sync and async mode which is never fun. Loops over execEditLength until a value
    // is produced.
    if (callback) {
      (function exec() {
        setTimeout(function () {
          // This should not happen, but we want to be safe.
          /* istanbul ignore next */
          if (editLength > maxEditLength) {
            return callback();
          }

          if (!execEditLength()) {
            exec();
          }
        }, 0);
      })();
    } else {
      while (editLength <= maxEditLength) {
        var ret = execEditLength();
        if (ret) {
          return ret;
        }
      }
    }
  },
  /*istanbul ignore start*/ /*istanbul ignore end*/pushComponent: function pushComponent(components, added, removed) {
    var last = components[components.length - 1];
    if (last && last.added === added && last.removed === removed) {
      // We need to clone here as the component clone operation is just
      // as shallow array clone
      components[components.length - 1] = { count: last.count + 1, added: added, removed: removed };
    } else {
      components.push({ count: 1, added: added, removed: removed });
    }
  },
  /*istanbul ignore start*/ /*istanbul ignore end*/extractCommon: function extractCommon(basePath, newString, oldString, diagonalPath) {
    var newLen = newString.length,
        oldLen = oldString.length,
        newPos = basePath.newPos,
        oldPos = newPos - diagonalPath,
        commonCount = 0;
    while (newPos + 1 < newLen && oldPos + 1 < oldLen && this.equals(newString[newPos + 1], oldString[oldPos + 1])) {
      newPos++;
      oldPos++;
      commonCount++;
    }

    if (commonCount) {
      basePath.components.push({ count: commonCount });
    }

    basePath.newPos = newPos;
    return oldPos;
  },
  /*istanbul ignore start*/ /*istanbul ignore end*/equals: function equals(left, right) {
    return left === right || this.options.ignoreCase && left.toLowerCase() === right.toLowerCase();
  },
  /*istanbul ignore start*/ /*istanbul ignore end*/removeEmpty: function removeEmpty(array) {
    var ret = [];
    for (var i = 0; i < array.length; i++) {
      if (array[i]) {
        ret.push(array[i]);
      }
    }
    return ret;
  },
  /*istanbul ignore start*/ /*istanbul ignore end*/castInput: function castInput(value) {
    return value;
  },
  /*istanbul ignore start*/ /*istanbul ignore end*/tokenize: function tokenize(value) {
    return value.split('');
  },
  /*istanbul ignore start*/ /*istanbul ignore end*/join: function join(chars) {
    return chars.join('');
  }
};

function buildValues(diff, components, newString, oldString, useLongestToken) {
  var componentPos = 0,
      componentLen = components.length,
      newPos = 0,
      oldPos = 0;

  for (; componentPos < componentLen; componentPos++) {
    var component = components[componentPos];
    if (!component.removed) {
      if (!component.added && useLongestToken) {
        var value = newString.slice(newPos, newPos + component.count);
        value = value.map(function (value, i) {
          var oldValue = oldString[oldPos + i];
          return oldValue.length > value.length ? oldValue : value;
        });

        component.value = diff.join(value);
      } else {
        component.value = diff.join(newString.slice(newPos, newPos + component.count));
      }
      newPos += component.count;

      // Common case
      if (!component.added) {
        oldPos += component.count;
      }
    } else {
      component.value = diff.join(oldString.slice(oldPos, oldPos + component.count));
      oldPos += component.count;

      // Reverse add and remove so removes are output first to match common convention
      // The diffing algorithm is tied to add then remove output and this is the simplest
      // route to get the desired output with minimal overhead.
      if (componentPos && components[componentPos - 1].added) {
        var tmp = components[componentPos - 1];
        components[componentPos - 1] = components[componentPos];
        components[componentPos] = tmp;
      }
    }
  }

  // Special case handle for when one terminal is ignored. For this case we merge the
  // terminal into the prior string and drop the change.
  var lastComponent = components[componentLen - 1];
  if (componentLen > 1 && (lastComponent.added || lastComponent.removed) && diff.equals('', lastComponent.value)) {
    components[componentLen - 2].value += lastComponent.value;
    components.pop();
  }

  return components;
}

function clonePath(path) {
  return { newPos: path.newPos, components: path.components.slice(0) };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
