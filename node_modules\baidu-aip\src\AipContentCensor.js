'use strict';
/**
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file AipImageCensor
 * <AUTHOR>
 */
const BaseClient = require("./client/baseClient");
const RequestInfo = require("./client/requestInfo");
const objectTools = require("./util/objectTools");
const HttpClient = require("./http/httpClient");
const HttpClientJson = require("./http/httpClientExt");
const httpHeader = require("./const/httpHeader");
const CONTENT_TYPE_JSON = 'application/json';
const METHOD_POST = 'POST';
const PATH_USER_DEFINED = '/rest/2.0/solution/v1/img_censor/user_defined';
const PATH_ANTIPORN_GIF = '/rest/2.0/antiporn/v1/detect_gif';
const PATH_FACEAUDIT = '/rest/2.0/solution/v1/face_audit';
const PATH_COMBOCENSOR = '/api/v1/solution/direct/img_censor';
const PATH_REPORT = '/rpc/2.0/feedback/v1/report';
const PATH_ANTIPORN = '/rest/2.0/antiporn/v1/detect';
const PATH_ANTITERROR = '/rest/2.0/antiterror/v1/detect';
const PATH_ANTISPAM = '/rest/2.0/antispam/v2/spam';
/**
 * AipContentCensor类，构造调用图像审核对象
 *
 * @class
 * @extends BaseClient
 * @constructor
 * @param {string} appid appid.
 * @param {string} ak  access key.
 * @param {string} sk  security key.
 */
class AipImageCensor extends BaseClient {
    commonImpl(param) {
        let httpClient = new HttpClient();
        let apiUrl = param.targetPath;
        delete param.targetPath;
        let requestInfo = new RequestInfo(apiUrl, param, METHOD_POST);
        return this.doRequest(requestInfo, httpClient);
    }
    jsonRequestImpl(param) {
        let httpClient = new HttpClientJson();
        let apiUrl = param.targetPath;
        delete param.targetPath;
        let requestInfo = new RequestInfo(apiUrl, param, METHOD_POST, false, {
            [httpHeader.CONTENT_TYPE]: CONTENT_TYPE_JSON
        });
        return this.doRequest(requestInfo, httpClient);
    }
    antiPornGif(image, options) {
        let param = {
            image: image,
            targetPath: PATH_ANTIPORN_GIF
        };
        return this.commonImpl(objectTools.merge(param, options));
    }
    antiPorn(image, options) {
        let param = {
            image: image,
            targetPath: PATH_ANTIPORN
        };
        return this.commonImpl(objectTools.merge(param, options));
    }
    antiTerror(image, options) {
        let param = {
            image: image,
            targetPath: PATH_ANTITERROR
        };
        return this.commonImpl(objectTools.merge(param, options));
    }
    antiSpam(content, options) {
        let param = {
            content: content,
            targetPath: PATH_ANTISPAM
        };
        return this.commonImpl(objectTools.merge(param, options));
    }
    faceAudit(images, type, configId) {
        let param = { configId: configId };
        if (type === 'url') {
            images = images.map(function (elm) {
                return encodeURIComponent(elm);
            });
            param.imgUrls = images.join(',');
        }
        if (type === 'base64') {
            param.images = images.join(',');
        }
        param.targetPath = PATH_FACEAUDIT;
        return this.commonImpl(param);
    }
    imageCensorUserDefined(image, type) {
        let param = {};
        if (type === 'url') {
            param.imgUrl = image;
        }
        if (type === 'base64') {
            param.image = image;
        }
        param.targetPath = PATH_USER_DEFINED;
        return this.commonImpl(param);
    }
    imageCensorComb(image, type, scenes, scenesConf) {
        let param = {};
        if (type === 'url') {
            param.imgUrl = image;
        }
        if (type === 'base64') {
            param.image = image;
        }
        param.scenes = scenes;
        param.sceneConf = scenesConf;
        param.targetPath = PATH_COMBOCENSOR;
        return this.jsonRequestImpl(param);
    }
    report(feedback) {
        let param = {};
        param.feedback = feedback;
        param.targetPath = PATH_REPORT;
        return this.jsonRequestImpl(param);
    }
}
exports.default = AipImageCensor;
// @ts-ignore
Object.assign(AipImageCensor, exports);
module.exports = AipImageCensor;
//# sourceMappingURL=data:application/json;base64,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