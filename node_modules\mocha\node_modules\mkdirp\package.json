{"name": "mkdirp", "description": "Recursively mkdir, like `mkdir -p`", "version": "0.5.1", "author": "<PERSON> <<EMAIL>> (http://substack.net)", "main": "index.js", "keywords": ["mkdir", "directory"], "repository": {"type": "git", "url": "https://github.com/substack/node-mkdirp.git"}, "scripts": {"test": "tap test/*.js"}, "dependencies": {"minimist": "0.0.8"}, "devDependencies": {"tap": "1", "mock-fs": "2 >=2.7.0"}, "bin": "bin/cmd.js", "license": "MIT"}