import { create<PERSON>orker, Worker } from 'tesseract.js';

export interface OCRResult {
  text: string;
  confidence: number;
  words: Array<{
    text: string;
    confidence: number;
    bbox: {
      x0: number;
      y0: number;
      x1: number;
      y1: number;
    };
  }>;
}

export interface TextMatch {
  text: string;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  confidence: number;
}

class OCRService {
  private worker: Worker | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.worker = await createWorker('eng');
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize OCR worker:', error);
      throw new Error('Failed to initialize OCR service');
    }
  }

  async recognizeText(imageFile: File): Promise<OCRResult> {
    if (!this.worker || !this.isInitialized) {
      await this.initialize();
    }

    if (!this.worker) {
      throw new Error('OCR worker not initialized');
    }

    try {
      const { data } = await this.worker.recognize(imageFile);
      
      return {
        text: data.text,
        confidence: data.confidence,
        words: data.words.map(word => ({
          text: word.text,
          confidence: word.confidence,
          bbox: word.bbox
        }))
      };
    } catch (error) {
      console.error('OCR recognition failed:', error);
      throw new Error('Failed to recognize text in image');
    }
  }

  findTextMatches(ocrResult: OCRResult, targetTexts: string[]): TextMatch[] {
    const matches: TextMatch[] = [];
    const normalizedTargets = targetTexts.map(text => text.toLowerCase().trim());

    // Check each word from OCR results
    ocrResult.words.forEach(word => {
      const normalizedWord = word.text.toLowerCase().trim();
      
      // Direct word match
      if (normalizedTargets.includes(normalizedWord)) {
        matches.push({
          text: word.text,
          bbox: word.bbox,
          confidence: word.confidence
        });
      }
      
      // Partial match (for phrases)
      normalizedTargets.forEach(target => {
        if (target.includes(' ')) { // Multi-word phrase
          // This is a simplified approach - in a real implementation,
          // you'd want to group consecutive words and check for phrase matches
          if (normalizedWord.includes(target.split(' ')[0])) {
            matches.push({
              text: word.text,
              bbox: word.bbox,
              confidence: word.confidence
            });
          }
        }
      });
    });

    return matches;
  }

  async terminate(): Promise<void> {
    if (this.worker) {
      await this.worker.terminate();
      this.worker = null;
      this.isInitialized = false;
    }
  }
}

// Export a singleton instance
export const ocrService = new OCRService();

// Utility function to read text file content
export async function readTextFile(file: File): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const content = event.target?.result as string;
      const lines = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);
      resolve(lines);
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read text file'));
    };
    
    reader.readAsText(file);
  });
}
