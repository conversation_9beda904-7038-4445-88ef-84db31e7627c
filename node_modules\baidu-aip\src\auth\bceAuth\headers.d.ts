/**
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file src/headers.js
 * <AUTHOR>
 */
export declare const CONTENT_TYPE = "Content-Type";
export declare const CONTENT_LENGTH = "Content-Length";
export declare const CONTENT_MD5 = "Content-MD5";
export declare const CONTENT_ENCODING = "Content-Encoding";
export declare const CONTENT_DISPOSITION = "Content-Disposition";
export declare const ETAG = "ETag";
export declare const CONNECTION = "Connection";
export declare const HOST = "Host";
export declare const USER_AGENT = "User-Agent";
export declare const CACHE_CONTROL = "Cache-Control";
export declare const EXPIRES = "Expires";
export declare const AUTHORIZATION = "Authorization";
export declare const X_BCE_DATE = "x-bce-date";
export declare const X_BCE_ACL = "x-bce-acl";
export declare const X_BCE_REQUEST_ID = "x-bce-request-id";
export declare const X_BCE_CONTENT_SHA256 = "x-bce-content-sha256";
export declare const X_BCE_OBJECT_ACL = "x-bce-object-acl";
export declare const X_BCE_OBJECT_GRANT_READ = "x-bce-object-grant-read";
export declare const X_HTTP_HEADERS = "http_headers";
export declare const X_BODY = "body";
export declare const X_STATUS_CODE = "status_code";
export declare const X_MESSAGE = "message";
export declare const X_CODE = "code";
export declare const X_REQUEST_ID = "request_id";
export declare const SESSION_TOKEN = "x-bce-security-token";
export declare const X_VOD_MEDIA_TITLE = "x-vod-media-title";
export declare const X_VOD_MEDIA_DESCRIPTION = "x-vod-media-description";
export declare const ACCEPT_ENCODING = "accept-encoding";
export declare const ACCEPT = "accept";
