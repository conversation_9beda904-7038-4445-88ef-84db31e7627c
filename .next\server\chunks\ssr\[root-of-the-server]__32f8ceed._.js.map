{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/utils/baiduOcr.ts"], "sourcesContent": ["export interface OCRResult {\n  text: string;\n  confidence: number;\n  words: Array<{\n    text: string;\n    confidence: number;\n    bbox: {\n      x0: number;\n      y0: number;\n      x1: number;\n      y1: number;\n    };\n  }>;\n}\n\nexport interface TextMatch {\n  text: string;\n  bbox: {\n    x0: number;\n    y0: number;\n    x1: number;\n    y1: number;\n  };\n  confidence: number;\n}\n\nclass BaiduOCRService {\n  async recognizeText(imageFile: File): Promise<OCRResult> {\n    try {\n      const formData = new FormData();\n      formData.append('image', imageFile);\n\n      // 首先尝试真实的百度OCR API\n      let response = await fetch('/api/ocr', {\n        method: 'POST',\n        body: formData,\n      });\n\n      // 如果百度OCR API失败（可能是未配置），使用测试API\n      if (!response.ok) {\n        console.warn('Baidu OCR API not available, using test OCR...');\n        response = await fetch('/api/test-ocr', {\n          method: 'POST',\n          body: formData,\n        });\n      }\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'OCR request failed');\n      }\n\n      const result = await response.json();\n      return result;\n    } catch (error) {\n      console.error('OCR recognition failed:', error);\n      throw new Error(error instanceof Error ? error.message : 'Failed to recognize text in image');\n    }\n  }\n\n  findTextMatches(ocrResult: OCRResult, targetTexts: string[]): TextMatch[] {\n    const matches: TextMatch[] = [];\n    const normalizedTargets = targetTexts.map(text => text.toLowerCase().trim());\n\n    // 检查每个OCR识别的词\n    ocrResult.words.forEach(word => {\n      const normalizedWord = word.text.toLowerCase().trim();\n      \n      // 直接匹配\n      if (normalizedTargets.includes(normalizedWord)) {\n        matches.push({\n          text: word.text,\n          bbox: word.bbox,\n          confidence: word.confidence\n        });\n        return;\n      }\n      \n      // 部分匹配 - 检查是否包含目标文字\n      normalizedTargets.forEach(target => {\n        if (normalizedWord.includes(target) || target.includes(normalizedWord)) {\n          matches.push({\n            text: word.text,\n            bbox: word.bbox,\n            confidence: word.confidence\n          });\n        }\n      });\n      \n      // 模糊匹配 - 处理可能的OCR识别错误\n      normalizedTargets.forEach(target => {\n        if (this.fuzzyMatch(normalizedWord, target)) {\n          matches.push({\n            text: word.text,\n            bbox: word.bbox,\n            confidence: word.confidence * 0.8 // 降低模糊匹配的置信度\n          });\n        }\n      });\n    });\n\n    // 去重\n    const uniqueMatches = matches.filter((match, index, self) => \n      index === self.findIndex(m => \n        m.bbox.x0 === match.bbox.x0 && \n        m.bbox.y0 === match.bbox.y0 &&\n        m.text === match.text\n      )\n    );\n\n    return uniqueMatches;\n  }\n\n  // 模糊匹配算法 - 处理OCR可能的识别错误\n  private fuzzyMatch(word: string, target: string): boolean {\n    if (word.length < 2 || target.length < 2) return false;\n    \n    // 计算编辑距离\n    const editDistance = this.levenshteinDistance(word, target);\n    const maxLength = Math.max(word.length, target.length);\n    \n    // 如果编辑距离小于长度的30%，认为是匹配的\n    return editDistance / maxLength < 0.3;\n  }\n\n  // 计算编辑距离\n  private levenshteinDistance(str1: string, str2: string): number {\n    const matrix = [];\n    \n    for (let i = 0; i <= str2.length; i++) {\n      matrix[i] = [i];\n    }\n    \n    for (let j = 0; j <= str1.length; j++) {\n      matrix[0][j] = j;\n    }\n    \n    for (let i = 1; i <= str2.length; i++) {\n      for (let j = 1; j <= str1.length; j++) {\n        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {\n          matrix[i][j] = matrix[i - 1][j - 1];\n        } else {\n          matrix[i][j] = Math.min(\n            matrix[i - 1][j - 1] + 1, // 替换\n            matrix[i][j - 1] + 1,     // 插入\n            matrix[i - 1][j] + 1      // 删除\n          );\n        }\n      }\n    }\n    \n    return matrix[str2.length][str1.length];\n  }\n}\n\n// 导出单例实例\nexport const baiduOcrService = new BaiduOCRService();\n\n// 读取文本文件内容的工具函数\nexport async function readTextFile(file: File): Promise<string[]> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    \n    reader.onload = (event) => {\n      const content = event.target?.result as string;\n      const lines = content\n        .split('\\n')\n        .map(line => line.trim())\n        .filter(line => line.length > 0);\n      resolve(lines);\n    };\n    \n    reader.onerror = () => {\n      reject(new Error('Failed to read text file'));\n    };\n    \n    reader.readAsText(file);\n  });\n}\n"], "names": [], "mappings": ";;;;AA0BA,MAAM;IACJ,MAAM,cAAc,SAAe,EAAsB;QACvD,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,SAAS;YAEzB,mBAAmB;YACnB,IAAI,WAAW,MAAM,MAAM,YAAY;gBACrC,QAAQ;gBACR,MAAM;YACR;YAEA,gCAAgC;YAChC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC;gBACb,WAAW,MAAM,MAAM,iBAAiB;oBACtC,QAAQ;oBACR,MAAM;gBACR;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAC3D;IACF;IAEA,gBAAgB,SAAoB,EAAE,WAAqB,EAAe;QACxE,MAAM,UAAuB,EAAE;QAC/B,MAAM,oBAAoB,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,WAAW,GAAG,IAAI;QAEzE,cAAc;QACd,UAAU,KAAK,CAAC,OAAO,CAAC,CAAA;YACtB,MAAM,iBAAiB,KAAK,IAAI,CAAC,WAAW,GAAG,IAAI;YAEnD,OAAO;YACP,IAAI,kBAAkB,QAAQ,CAAC,iBAAiB;gBAC9C,QAAQ,IAAI,CAAC;oBACX,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,YAAY,KAAK,UAAU;gBAC7B;gBACA;YACF;YAEA,oBAAoB;YACpB,kBAAkB,OAAO,CAAC,CAAA;gBACxB,IAAI,eAAe,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC,iBAAiB;oBACtE,QAAQ,IAAI,CAAC;wBACX,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,YAAY,KAAK,UAAU;oBAC7B;gBACF;YACF;YAEA,sBAAsB;YACtB,kBAAkB,OAAO,CAAC,CAAA;gBACxB,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,SAAS;oBAC3C,QAAQ,IAAI,CAAC;wBACX,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf,YAAY,KAAK,UAAU,GAAG,IAAI,aAAa;oBACjD;gBACF;YACF;QACF;QAEA,KAAK;QACL,MAAM,gBAAgB,QAAQ,MAAM,CAAC,CAAC,OAAO,OAAO,OAClD,UAAU,KAAK,SAAS,CAAC,CAAA,IACvB,EAAE,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,EAAE,IAC3B,EAAE,IAAI,CAAC,EAAE,KAAK,MAAM,IAAI,CAAC,EAAE,IAC3B,EAAE,IAAI,KAAK,MAAM,IAAI;QAIzB,OAAO;IACT;IAEA,wBAAwB;IAChB,WAAW,IAAY,EAAE,MAAc,EAAW;QACxD,IAAI,KAAK,MAAM,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG,OAAO;QAEjD,SAAS;QACT,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,MAAM;QACpD,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE,OAAO,MAAM;QAErD,wBAAwB;QACxB,OAAO,eAAe,YAAY;IACpC;IAEA,SAAS;IACD,oBAAoB,IAAY,EAAE,IAAY,EAAU;QAC9D,MAAM,SAAS,EAAE;QAEjB,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;YACrC,MAAM,CAAC,EAAE,GAAG;gBAAC;aAAE;QACjB;QAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;YACrC,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG;QACjB;QAEA,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;YACrC,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,MAAM,EAAE,IAAK;gBACrC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,IAAI,IAAI;oBAC7C,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE;gBACrC,OAAO;oBACL,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,GAAG,CACrB,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,GACvB,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,GACnB,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,EAAO,KAAK;;gBAEnC;YACF;QACF;QAEA,OAAO,MAAM,CAAC,KAAK,MAAM,CAAC,CAAC,KAAK,MAAM,CAAC;IACzC;AACF;AAGO,MAAM,kBAAkB,IAAI;AAG5B,eAAe,aAAa,IAAU;IAC3C,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QAEnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,MAAM,MAAM,EAAE;YAC9B,MAAM,QAAQ,QACX,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAChC,QAAQ;QACV;QAEA,OAAO,OAAO,GAAG;YACf,OAAO,IAAI,MAAM;QACnB;QAEA,OAAO,UAAU,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/utils/imageProcessor.ts"], "sourcesContent": ["import { TextMatch } from './ocr';\n\nexport interface ProcessingOptions {\n  fillColor?: string;\n  padding?: number;\n  blurRadius?: number;\n}\n\nexport class ImageProcessor {\n  private canvas: HTMLCanvasElement | null = null;\n  private ctx: CanvasRenderingContext2D | null = null;\n\n  private initializeCanvas() {\n    if (typeof window === 'undefined') {\n      throw new Error('ImageProcessor can only be used in the browser');\n    }\n\n    if (!this.canvas) {\n      this.canvas = document.createElement('canvas');\n      const context = this.canvas.getContext('2d');\n      if (!context) {\n        throw new Error('Failed to get canvas 2D context');\n      }\n      this.ctx = context;\n    }\n  }\n\n  async loadImage(file: File): Promise<HTMLImageElement> {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      const url = URL.createObjectURL(file);\n      \n      img.onload = () => {\n        URL.revokeObjectURL(url);\n        resolve(img);\n      };\n      \n      img.onerror = () => {\n        URL.revokeObjectURL(url);\n        reject(new Error('Failed to load image'));\n      };\n      \n      img.src = url;\n    });\n  }\n\n  async removeTextFromImage(\n    imageFile: File,\n    textMatches: TextMatch[],\n    options: ProcessingOptions = {}\n  ): Promise<Blob> {\n    this.initializeCanvas();\n    if (!this.canvas || !this.ctx) {\n      throw new Error('Canvas not initialized');\n    }\n\n    const img = await this.loadImage(imageFile);\n\n    // Set canvas dimensions to match image\n    this.canvas.width = img.width;\n    this.canvas.height = img.height;\n\n    // Draw the original image\n    this.ctx.drawImage(img, 0, 0);\n\n    // Remove text by covering matched areas\n    await this.removeTextAreas(textMatches, options);\n\n    return new Promise((resolve, reject) => {\n      this.canvas!.toBlob((blob) => {\n        if (blob) {\n          resolve(blob);\n        } else {\n          reject(new Error('Failed to create processed image blob'));\n        }\n      }, 'image/png');\n    });\n  }\n\n  private async removeTextAreas(textMatches: TextMatch[], options: ProcessingOptions): Promise<void> {\n    const { fillColor = '#FFFFFF', padding = 2, blurRadius = 0 } = options;\n    \n    for (const match of textMatches) {\n      const { bbox } = match;\n      \n      // Add padding to the bounding box\n      const x = Math.max(0, bbox.x0 - padding);\n      const y = Math.max(0, bbox.y0 - padding);\n      const width = Math.min(this.canvas.width - x, bbox.x1 - bbox.x0 + 2 * padding);\n      const height = Math.min(this.canvas.height - y, bbox.y1 - bbox.y0 + 2 * padding);\n      \n      if (blurRadius > 0) {\n        // Apply blur effect for more natural text removal\n        await this.applyInpainting(x, y, width, height);\n      } else {\n        // Simple fill with solid color\n        this.ctx.fillStyle = fillColor;\n        this.ctx.fillRect(x, y, width, height);\n      }\n    }\n  }\n\n  private async applyInpainting(x: number, y: number, width: number, height: number): Promise<void> {\n    // Get surrounding pixels for color sampling\n    const surroundingData = this.getSurroundingPixels(x, y, width, height);\n    \n    // Calculate average color from surrounding pixels\n    const avgColor = this.calculateAverageColor(surroundingData);\n    \n    // Create gradient fill for more natural appearance\n    const gradient = this.ctx.createLinearGradient(x, y, x + width, y + height);\n    gradient.addColorStop(0, avgColor);\n    gradient.addColorStop(1, this.adjustBrightness(avgColor, 0.9));\n    \n    this.ctx.fillStyle = gradient;\n    this.ctx.fillRect(x, y, width, height);\n    \n    // Apply slight blur effect\n    this.applyGaussianBlur(x, y, width, height, 1);\n  }\n\n  private getSurroundingPixels(x: number, y: number, width: number, height: number): ImageData {\n    const margin = 5;\n    const surroundX = Math.max(0, x - margin);\n    const surroundY = Math.max(0, y - margin);\n    const surroundWidth = Math.min(this.canvas.width - surroundX, width + 2 * margin);\n    const surroundHeight = Math.min(this.canvas.height - surroundY, height + 2 * margin);\n    \n    return this.ctx.getImageData(surroundX, surroundY, surroundWidth, surroundHeight);\n  }\n\n  private calculateAverageColor(imageData: ImageData): string {\n    const data = imageData.data;\n    let r = 0, g = 0, b = 0;\n    let pixelCount = 0;\n    \n    for (let i = 0; i < data.length; i += 4) {\n      r += data[i];\n      g += data[i + 1];\n      b += data[i + 2];\n      pixelCount++;\n    }\n    \n    r = Math.round(r / pixelCount);\n    g = Math.round(g / pixelCount);\n    b = Math.round(b / pixelCount);\n    \n    return `rgb(${r}, ${g}, ${b})`;\n  }\n\n  private adjustBrightness(color: string, factor: number): string {\n    const rgb = color.match(/\\d+/g);\n    if (!rgb) return color;\n    \n    const r = Math.round(parseInt(rgb[0]) * factor);\n    const g = Math.round(parseInt(rgb[1]) * factor);\n    const b = Math.round(parseInt(rgb[2]) * factor);\n    \n    return `rgb(${r}, ${g}, ${b})`;\n  }\n\n  private applyGaussianBlur(x: number, y: number, width: number, height: number, radius: number): void {\n    // Simple box blur approximation of Gaussian blur\n    const imageData = this.ctx.getImageData(x, y, width, height);\n    const blurred = this.boxBlur(imageData, radius);\n    this.ctx.putImageData(blurred, x, y);\n  }\n\n  private boxBlur(imageData: ImageData, radius: number): ImageData {\n    const data = new Uint8ClampedArray(imageData.data);\n    const width = imageData.width;\n    const height = imageData.height;\n    \n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        let r = 0, g = 0, b = 0, a = 0;\n        let count = 0;\n        \n        for (let dy = -radius; dy <= radius; dy++) {\n          for (let dx = -radius; dx <= radius; dx++) {\n            const nx = x + dx;\n            const ny = y + dy;\n            \n            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {\n              const idx = (ny * width + nx) * 4;\n              r += imageData.data[idx];\n              g += imageData.data[idx + 1];\n              b += imageData.data[idx + 2];\n              a += imageData.data[idx + 3];\n              count++;\n            }\n          }\n        }\n        \n        const idx = (y * width + x) * 4;\n        data[idx] = r / count;\n        data[idx + 1] = g / count;\n        data[idx + 2] = b / count;\n        data[idx + 3] = a / count;\n      }\n    }\n    \n    return new ImageData(data, width, height);\n  }\n\n  getCanvas(): HTMLCanvasElement {\n    return this.canvas;\n  }\n}\n\n// Create a function to get the image processor instance (lazy initialization)\nlet imageProcessorInstance: ImageProcessor | null = null;\n\nexport function getImageProcessor(): ImageProcessor {\n  if (!imageProcessorInstance) {\n    imageProcessorInstance = new ImageProcessor();\n  }\n  return imageProcessorInstance;\n}\n"], "names": [], "mappings": ";;;;AAQO,MAAM;IACH,SAAmC,KAAK;IACxC,MAAuC,KAAK;IAE5C,mBAAmB;QACzB,wCAAmC;YACjC,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,SAAS,aAAa,CAAC;YACrC,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACvC,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,GAAG,GAAG;QACb;IACF;IAEA,MAAM,UAAU,IAAU,EAA6B;QACrD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,IAAI,MAAM,GAAG;gBACX,IAAI,eAAe,CAAC;gBACpB,QAAQ;YACV;YAEA,IAAI,OAAO,GAAG;gBACZ,IAAI,eAAe,CAAC;gBACpB,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,GAAG,GAAG;QACZ;IACF;IAEA,MAAM,oBACJ,SAAe,EACf,WAAwB,EACxB,UAA6B,CAAC,CAAC,EAChB;QACf,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC;QAEjC,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM;QAE/B,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG;QAE3B,wCAAwC;QACxC,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa;QAExC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,MAAM,CAAE,MAAM,CAAC,CAAC;gBACnB,IAAI,MAAM;oBACR,QAAQ;gBACV,OAAO;oBACL,OAAO,IAAI,MAAM;gBACnB;YACF,GAAG;QACL;IACF;IAEA,MAAc,gBAAgB,WAAwB,EAAE,OAA0B,EAAiB;QACjG,MAAM,EAAE,YAAY,SAAS,EAAE,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,GAAG;QAE/D,KAAK,MAAM,SAAS,YAAa;YAC/B,MAAM,EAAE,IAAI,EAAE,GAAG;YAEjB,kCAAkC;YAClC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG;YAChC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG;YAChC,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI;YACtE,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI;YAExE,IAAI,aAAa,GAAG;gBAClB,kDAAkD;gBAClD,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,OAAO;YAC1C,OAAO;gBACL,+BAA+B;gBAC/B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG;gBACrB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO;YACjC;QACF;IACF;IAEA,MAAc,gBAAgB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAiB;QAChG,4CAA4C;QAC5C,MAAM,kBAAkB,IAAI,CAAC,oBAAoB,CAAC,GAAG,GAAG,OAAO;QAE/D,kDAAkD;QAClD,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC;QAE5C,mDAAmD;QACnD,MAAM,WAAW,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,GAAG,IAAI,OAAO,IAAI;QACpE,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAEzD,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG;QACrB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO;QAE/B,2BAA2B;QAC3B,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,QAAQ;IAC9C;IAEQ,qBAAqB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAa;QAC3F,MAAM,SAAS;QACf,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI;QAClC,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI;QAClC,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,WAAW,QAAQ,IAAI;QAC1E,MAAM,iBAAiB,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,WAAW,SAAS,IAAI;QAE7E,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,WAAW,eAAe;IACpE;IAEQ,sBAAsB,SAAoB,EAAU;QAC1D,MAAM,OAAO,UAAU,IAAI;QAC3B,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI;QACtB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,KAAK,IAAI,CAAC,EAAE;YACZ,KAAK,IAAI,CAAC,IAAI,EAAE;YAChB,KAAK,IAAI,CAAC,IAAI,EAAE;YAChB;QACF;QAEA,IAAI,KAAK,KAAK,CAAC,IAAI;QACnB,IAAI,KAAK,KAAK,CAAC,IAAI;QACnB,IAAI,KAAK,KAAK,CAAC,IAAI;QAEnB,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC;IAEQ,iBAAiB,KAAa,EAAE,MAAc,EAAU;QAC9D,MAAM,MAAM,MAAM,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,OAAO;QAEjB,MAAM,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI;QACxC,MAAM,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI;QACxC,MAAM,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI;QAExC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC;IAEQ,kBAAkB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,MAAc,EAAQ;QACnG,iDAAiD;QACjD,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO;QACrD,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,WAAW;QACxC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,GAAG;IACpC;IAEQ,QAAQ,SAAoB,EAAE,MAAc,EAAa;QAC/D,MAAM,OAAO,IAAI,kBAAkB,UAAU,IAAI;QACjD,MAAM,QAAQ,UAAU,KAAK;QAC7B,MAAM,SAAS,UAAU,MAAM;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;gBAC7B,IAAI,QAAQ;gBAEZ,IAAK,IAAI,KAAK,CAAC,QAAQ,MAAM,QAAQ,KAAM;oBACzC,IAAK,IAAI,KAAK,CAAC,QAAQ,MAAM,QAAQ,KAAM;wBACzC,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBAEf,IAAI,MAAM,KAAK,KAAK,SAAS,MAAM,KAAK,KAAK,QAAQ;4BACnD,MAAM,MAAM,CAAC,KAAK,QAAQ,EAAE,IAAI;4BAChC,KAAK,UAAU,IAAI,CAAC,IAAI;4BACxB,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BAC5B,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BAC5B,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BAC5B;wBACF;oBACF;gBACF;gBAEA,MAAM,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI;gBAC9B,IAAI,CAAC,IAAI,GAAG,IAAI;gBAChB,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;gBACpB,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;gBACpB,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;YACtB;QACF;QAEA,OAAO,IAAI,UAAU,MAAM,OAAO;IACpC;IAEA,YAA+B;QAC7B,OAAO,IAAI,CAAC,MAAM;IACpB;AACF;AAEA,8EAA8E;AAC9E,IAAI,yBAAgD;AAE7C,SAAS;IACd,IAAI,CAAC,wBAAwB;QAC3B,yBAAyB,IAAI;IAC/B;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Upload, FileText, Image as ImageIcon, Download, Loader2, <PERSON><PERSON>s, Zap, Eye, RotateCcw } from 'lucide-react';\nimport { baiduOcrService, readTextFile } from '@/utils/baiduOcr';\nimport { getImageProcessor } from '@/utils/imageProcessor';\nimport { saveAs } from 'file-saver';\n\nexport default function Home() {\n  const [imageFile, setImageFile] = useState<File | null>(null);\n  const [imageFiles, setImageFiles] = useState<File[]>([]);\n  const [textFile, setTextFile] = useState<File | null>(null);\n  const [batchMode, setBatchMode] = useState(false);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [originalImageUrl, setOriginalImageUrl] = useState<string>('');\n  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');\n  const [dragOver, setDragOver] = useState<'image' | 'text' | null>(null);\n  const [error, setError] = useState<string>('');\n  const [processingStatus, setProcessingStatus] = useState<string>('');\n  const [processedBlob, setProcessedBlob] = useState<Blob | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string>('');\n  const [textFileContent, setTextFileContent] = useState<string[]>([]);\n  const [processingOptions, setProcessingOptions] = useState({\n    fillColor: '#FFFFFF',\n    padding: 3,\n    useSmartFill: true,\n    blurRadius: 1\n  });\n  // 百度OCR自动支持中英文混合识别，不需要语言选择\n  const [processingProgress, setProcessingProgress] = useState(0);\n  const [processingStats, setProcessingStats] = useState({\n    totalWords: 0,\n    matchedWords: 0,\n    processingTime: 0\n  });\n\n  // Load saved settings on component mount\n  useEffect(() => {\n    const savedSettings = localStorage.getItem('textRemoverSettings');\n    if (savedSettings) {\n      try {\n        const settings = JSON.parse(savedSettings);\n        setProcessingOptions(prev => ({ ...prev, ...settings.processingOptions }));\n      } catch (error) {\n        console.error('Failed to load saved settings:', error);\n      }\n    }\n  }, []);\n\n  // Save settings when they change\n  useEffect(() => {\n    const settings = {\n      processingOptions\n    };\n    localStorage.setItem('textRemoverSettings', JSON.stringify(settings));\n  }, [processingOptions]);\n\n  // File validation\n  const validateImageFile = (file: File): boolean => {\n    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];\n    const maxSize = 10 * 1024 * 1024; // 10MB\n\n    if (!validTypes.includes(file.type)) {\n      setError('Please upload a valid image file (JPG, PNG, GIF, WebP, BMP)');\n      return false;\n    }\n\n    if (file.size > maxSize) {\n      setError('Image file size must be less than 10MB');\n      return false;\n    }\n\n    return true;\n  };\n\n  const validateTextFile = (file: File): boolean => {\n    const validTypes = ['text/plain'];\n    const maxSize = 1024 * 1024; // 1MB\n\n    if (!validTypes.includes(file.type) && !file.name.endsWith('.txt')) {\n      setError('Please upload a valid text file (.txt)');\n      return false;\n    }\n\n    if (file.size > maxSize) {\n      setError('Text file size must be less than 1MB');\n      return false;\n    }\n\n    return true;\n  };\n\n  // Handle image file selection\n  const handleImageFile = (file: File) => {\n    setError('');\n    setSuccessMessage('');\n    // Clear previous results\n    setProcessedImageUrl('');\n    setProcessedBlob(null);\n\n    if (validateImageFile(file)) {\n      setImageFile(file);\n      setOriginalImageUrl(URL.createObjectURL(file));\n    }\n  };\n\n  // Handle multiple image files selection\n  const handleImageFiles = (files: FileList) => {\n    setError('');\n    setSuccessMessage('');\n    const validFiles: File[] = [];\n\n    Array.from(files).forEach(file => {\n      if (validateImageFile(file)) {\n        validFiles.push(file);\n      }\n    });\n\n    if (validFiles.length > 0) {\n      setImageFiles(validFiles);\n      setSuccessMessage(`已选择 ${validFiles.length} 个图片文件进行批量处理`);\n    }\n  };\n\n  // Handle text file selection\n  const handleTextFile = async (file: File) => {\n    setError('');\n    setSuccessMessage('');\n    if (validateTextFile(file)) {\n      setTextFile(file);\n      try {\n        const content = await readTextFile(file);\n        setTextFileContent(content);\n        setSuccessMessage(`Loaded ${content.length} text entries to remove`);\n      } catch (error) {\n        setError('Failed to read text file content');\n      }\n    }\n  };\n\n  // Drag and drop handlers\n  const handleDragOver = (e: React.DragEvent, type: 'image' | 'text') => {\n    e.preventDefault();\n    setDragOver(type);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(null);\n  };\n\n  const handleDrop = (e: React.DragEvent, type: 'image' | 'text') => {\n    e.preventDefault();\n    setDragOver(null);\n\n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      if (type === 'image') {\n        if (batchMode && files.length > 1) {\n          const fileList = new DataTransfer();\n          files.forEach(file => fileList.items.add(file));\n          handleImageFiles(fileList.files);\n        } else {\n          handleImageFile(files[0]);\n        }\n      } else {\n        handleTextFile(files[0]);\n      }\n    }\n  };\n\n  // Main processing function\n  const processImage = async () => {\n    if (!imageFile || !textFile) {\n      setError('Please upload both an image and text file');\n      return;\n    }\n\n    setIsProcessing(true);\n    setError('');\n    setProcessingStatus('Initializing OCR...');\n    setProcessingProgress(0);\n    const startTime = Date.now();\n\n    try {\n      // 百度OCR不需要初始化，直接开始处理\n      setProcessingProgress(10);\n\n      // Read text file content\n      setProcessingStatus('Reading text file...');\n      setProcessingProgress(20);\n      const targetTexts = await readTextFile(textFile);\n\n      if (targetTexts.length === 0) {\n        throw new Error('Text file is empty or contains no valid text');\n      }\n\n      // 使用百度OCR分析图片\n      setProcessingStatus('使用百度OCR分析图片文字...');\n      setProcessingProgress(40);\n      const ocrResult = await baiduOcrService.recognizeText(imageFile);\n\n      // 查找匹配的文字\n      setProcessingStatus('查找匹配的文字...');\n      setProcessingProgress(60);\n      const textMatches = baiduOcrService.findTextMatches(ocrResult, targetTexts);\n\n      console.log(`OCR detected ${ocrResult.words.length} words, found ${textMatches.length} matches`);\n\n      // Update stats\n      setProcessingStats({\n        totalWords: ocrResult.words.length,\n        matchedWords: textMatches.length,\n        processingTime: Date.now() - startTime\n      });\n\n      if (textMatches.length === 0) {\n        setError(`No matching text found in the image. OCR detected: \"${ocrResult.text.substring(0, 100)}${ocrResult.text.length > 100 ? '...' : ''}\"`);\n        return;\n      }\n\n      // Process image to remove text\n      setProcessingStatus('Removing text from image...');\n      setProcessingProgress(80);\n      const imageProcessor = getImageProcessor();\n      const processedBlob = await imageProcessor.removeTextFromImage(\n        imageFile,\n        textMatches,\n        {\n          fillColor: processingOptions.useSmartFill ? undefined : processingOptions.fillColor,\n          padding: processingOptions.padding,\n          blurRadius: processingOptions.blurRadius\n        }\n      );\n\n      // Create URL for processed image\n      setProcessingProgress(95);\n      const processedUrl = URL.createObjectURL(processedBlob);\n      setProcessedImageUrl(processedUrl);\n      setProcessedBlob(processedBlob);\n\n      setProcessingProgress(100);\n      const finalTime = Date.now() - startTime;\n      setProcessingStats(prev => ({ ...prev, processingTime: finalTime }));\n      setSuccessMessage(`Successfully removed ${textMatches.length} text match${textMatches.length > 1 ? 'es' : ''} from the image in ${(finalTime/1000).toFixed(1)}s!`);\n      setProcessingStatus('');\n\n    } catch (error) {\n      console.error('Processing error:', error);\n      setError(error instanceof Error ? error.message : 'An error occurred during processing');\n    } finally {\n      setIsProcessing(false);\n      setProcessingStatus('');\n    }\n  };\n\n  // Download processed image\n  const downloadProcessedImage = () => {\n    if (processedBlob && imageFile) {\n      const fileName = `processed_${imageFile.name.replace(/\\.[^/.]+$/, '')}.png`;\n      saveAs(processedBlob, fileName);\n    }\n  };\n\n  // Reset all data\n  const resetAll = () => {\n    setImageFile(null);\n    setImageFiles([]);\n    setTextFile(null);\n    setOriginalImageUrl('');\n    setProcessedImageUrl('');\n    setProcessedBlob(null);\n    setError('');\n    setSuccessMessage('');\n    setTextFileContent([]);\n    setProcessingProgress(0);\n    setProcessingStats({ totalWords: 0, matchedWords: 0, processingTime: 0 });\n    setBatchMode(false);\n  };\n\n  // Cleanup function\n  useEffect(() => {\n    return () => {\n      // Cleanup object URLs to prevent memory leaks\n      if (originalImageUrl) {\n        URL.revokeObjectURL(originalImageUrl);\n      }\n      if (processedImageUrl) {\n        URL.revokeObjectURL(processedImageUrl);\n      }\n      // 百度OCR使用API调用，无需清理\n    };\n  }, [originalImageUrl, processedImageUrl]);\n\n  // Keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // Ctrl/Cmd + Enter to process image\n      if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {\n        if (imageFile && textFile && !isProcessing) {\n          e.preventDefault();\n          processImage();\n        }\n      }\n\n      // Ctrl/Cmd + D to download processed image\n      if ((e.ctrlKey || e.metaKey) && e.key === 'd') {\n        if (processedBlob) {\n          e.preventDefault();\n          downloadProcessedImage();\n        }\n      }\n\n      // Escape to clear error messages\n      if (e.key === 'Escape') {\n        setError('');\n        setSuccessMessage('');\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [imageFile, textFile, isProcessing, processedBlob]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"bg-blue-600 p-2 rounded-lg\">\n                <ImageIcon className=\"h-6 w-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Text Remover\n                </h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  使用OCR技术从图片中移除指定文字\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <button\n                onClick={resetAll}\n                className=\"inline-flex items-center px-3 py-2 text-sm bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                title=\"重置所有数据\"\n              >\n                <RotateCcw className=\"h-4 w-4 mr-1\" />\n                重置\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Instructions */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            使用说明:\n          </h2>\n          <ol className=\"list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-400 mb-4\">\n            <li>上传图片文件 (JPG, PNG, GIF 等格式)</li>\n            <li>上传包含要移除文字的文本文件</li>\n            <li>点击\"Process Image\"开始检测和移除匹配的文字</li>\n            <li>下载处理后的图片</li>\n          </ol>\n\n          <div className=\"border-t border-gray-200 dark:border-gray-700 pt-4\">\n            <h3 className=\"text-sm font-medium text-gray-900 dark:text-white mb-2\">\n              快捷键:\n            </h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-500 dark:text-gray-400\">\n              <div>\n                <kbd className=\"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded\">Ctrl+Enter</kbd>\n                <span className=\"ml-2\">处理图片</span>\n              </div>\n              <div>\n                <kbd className=\"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded\">Ctrl+D</kbd>\n                <span className=\"ml-2\">下载结果</span>\n              </div>\n              <div>\n                <kbd className=\"px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded\">Esc</kbd>\n                <span className=\"ml-2\">清除消息</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* File Upload Section */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          {/* Image Upload */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white flex items-center\">\n                <ImageIcon className=\"h-5 w-5 mr-2\" />\n                Upload Image\n              </h3>\n              <label className=\"flex items-center space-x-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={batchMode}\n                  onChange={(e) => setBatchMode(e.target.checked)}\n                  className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                />\n                <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                  批量模式\n                </span>\n              </label>\n            </div>\n            <div\n              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n                dragOver === 'image'\n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'\n              }`}\n              onDragOver={(e) => handleDragOver(e, 'image')}\n              onDragLeave={handleDragLeave}\n              onDrop={(e) => handleDrop(e, 'image')}\n            >\n              <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400 mb-2\">\n                Drag and drop your image here, or click to browse\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n                Supports JPG, PNG, GIF, WebP, BMP (max 10MB)\n              </p>\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                multiple={batchMode}\n                className=\"hidden\"\n                id=\"image-upload\"\n                onChange={(e) => {\n                  if (batchMode && e.target.files) {\n                    handleImageFiles(e.target.files);\n                  } else {\n                    const file = e.target.files?.[0];\n                    if (file) {\n                      handleImageFile(file);\n                    }\n                  }\n                }}\n              />\n              <label\n                htmlFor=\"image-upload\"\n                className=\"mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors\"\n              >\n                Choose Image\n              </label>\n            </div>\n            {imageFile && !batchMode && (\n              <div className=\"mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md\">\n                <p className=\"text-sm text-green-800 dark:text-green-400\">\n                  ✓ {imageFile.name} ({(imageFile.size / 1024 / 1024).toFixed(2)} MB)\n                </p>\n              </div>\n            )}\n\n            {batchMode && imageFiles.length > 0 && (\n              <div className=\"mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md\">\n                <p className=\"text-sm text-green-800 dark:text-green-400 mb-2\">\n                  ✓ 已选择 {imageFiles.length} 个图片文件\n                </p>\n                <div className=\"max-h-32 overflow-y-auto space-y-1\">\n                  {imageFiles.map((file, index) => (\n                    <div key={index} className=\"text-xs text-green-700 dark:text-green-300 truncate\">\n                      • {file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Text File Upload */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center\">\n              <FileText className=\"h-5 w-5 mr-2\" />\n              Upload Text File\n            </h3>\n            <div\n              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n                dragOver === 'text'\n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'\n              }`}\n              onDragOver={(e) => handleDragOver(e, 'text')}\n              onDragLeave={handleDragLeave}\n              onDrop={(e) => handleDrop(e, 'text')}\n            >\n              <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400 mb-2\">\n                Upload a text file with words to remove\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n                One word or phrase per line (max 1MB)\n              </p>\n              <input\n                type=\"file\"\n                accept=\".txt,.text\"\n                className=\"hidden\"\n                id=\"text-upload\"\n                onChange={(e) => {\n                  const file = e.target.files?.[0];\n                  if (file) {\n                    handleTextFile(file);\n                  }\n                }}\n              />\n              <label\n                htmlFor=\"text-upload\"\n                className=\"mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors\"\n              >\n                Choose Text File\n              </label>\n            </div>\n            {textFile && (\n              <div className=\"mt-4 space-y-2\">\n                <div className=\"p-3 bg-green-50 dark:bg-green-900/20 rounded-md\">\n                  <p className=\"text-sm text-green-800 dark:text-green-400\">\n                    ✓ {textFile.name} ({(textFile.size / 1024).toFixed(2)} KB)\n                  </p>\n                </div>\n                {textFileContent.length > 0 && (\n                  <div className=\"p-3 bg-gray-50 dark:bg-gray-700 rounded-md\">\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 mb-2\">\n                      Text to remove ({textFileContent.length} entries):\n                    </p>\n                    <div className=\"text-xs text-gray-800 dark:text-gray-200 max-h-20 overflow-y-auto\">\n                      {textFileContent.slice(0, 10).map((text, index) => (\n                        <div key={index} className=\"truncate\">• {text}</div>\n                      ))}\n                      {textFileContent.length > 10 && (\n                        <div className=\"text-gray-500 italic\">... and {textFileContent.length - 10} more</div>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8\">\n            <p className=\"text-red-800 dark:text-red-400 text-sm\">\n              ⚠️ {error}\n            </p>\n          </div>\n        )}\n\n        {/* Success Display */}\n        {successMessage && (\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-8\">\n            <p className=\"text-green-800 dark:text-green-400 text-sm\">\n              ✅ {successMessage}\n            </p>\n          </div>\n        )}\n\n        {/* Processing Options */}\n        {imageFile && textFile && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">\n              处理选项\n            </h3>\n\n            <div className=\"mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n              <div className=\"flex items-center space-x-2\">\n                <Zap className=\"h-5 w-5 text-blue-600 dark:text-blue-400\" />\n                <span className=\"text-sm font-medium text-blue-800 dark:text-blue-200\">\n                  使用百度OCR引擎\n                </span>\n              </div>\n              <p className=\"text-xs text-blue-600 dark:text-blue-300 mt-1\">\n                自动识别中英文混合文字，识别精度更高\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  填充颜色\n                </label>\n                <div className=\"flex items-center space-x-3\">\n                  <input\n                    type=\"color\"\n                    value={processingOptions.fillColor}\n                    onChange={(e) => setProcessingOptions(prev => ({ ...prev, fillColor: e.target.value }))}\n                    className=\"w-12 h-8 rounded border border-gray-300 dark:border-gray-600\"\n                  />\n                  <input\n                    type=\"text\"\n                    value={processingOptions.fillColor}\n                    onChange={(e) => setProcessingOptions(prev => ({ ...prev, fillColor: e.target.value }))}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n                    placeholder=\"#FFFFFF\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  边距填充: {processingOptions.padding}px\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"0\"\n                  max=\"10\"\n                  value={processingOptions.padding}\n                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, padding: parseInt(e.target.value) }))}\n                  className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\n                />\n              </div>\n\n              <div>\n                <label className=\"flex items-center space-x-2\">\n                  <input\n                    type=\"checkbox\"\n                    checked={processingOptions.useSmartFill}\n                    onChange={(e) => setProcessingOptions(prev => ({ ...prev, useSmartFill: e.target.checked }))}\n                    className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n                  />\n                  <span className=\"text-sm text-gray-700 dark:text-gray-300\">\n                    使用智能填充\n                  </span>\n                </label>\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                  根据周围像素智能选择填充颜色\n                </p>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  模糊半径: {processingOptions.blurRadius}px\n                </label>\n                <input\n                  type=\"range\"\n                  min=\"0\"\n                  max=\"5\"\n                  value={processingOptions.blurRadius}\n                  onChange={(e) => setProcessingOptions(prev => ({ ...prev, blurRadius: parseInt(e.target.value) }))}\n                  className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700\"\n                />\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Process Button */}\n        <div className=\"text-center mb-8\">\n          <button\n            disabled={!imageFile || !textFile || isProcessing}\n            onClick={processImage}\n            className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n          >\n            {isProcessing ? (\n              <>\n                <Loader2 className=\"h-5 w-5 mr-2 animate-spin\" />\n                {processingStatus || 'Processing...'}\n              </>\n            ) : (\n              'Process Image'\n            )}\n          </button>\n\n          {/* Progress Bar */}\n          {isProcessing && (\n            <div className=\"mt-4 max-w-md mx-auto\">\n              <div className=\"flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2\">\n                <span>处理进度</span>\n                <span>{processingProgress}%</span>\n              </div>\n              <div className=\"w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700\">\n                <div\n                  className=\"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out\"\n                  style={{ width: `${processingProgress}%` }}\n                ></div>\n              </div>\n            </div>\n          )}\n\n          {/* Processing Stats */}\n          {processingStats.totalWords > 0 && (\n            <div className=\"mt-4 text-sm text-gray-600 dark:text-gray-400\">\n              <div className=\"flex justify-center space-x-6\">\n                <span>检测到 {processingStats.totalWords} 个词</span>\n                <span>匹配 {processingStats.matchedWords} 个</span>\n                <span>耗时 {(processingStats.processingTime/1000).toFixed(1)}s</span>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Results Section */}\n        {(originalImageUrl || processedImageUrl) && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-6\">\n              Results\n            </h3>\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {/* Original Image */}\n              {originalImageUrl && (\n                <div>\n                  <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                    Original Image\n                  </h4>\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden\">\n                    <img\n                      src={originalImageUrl}\n                      alt=\"Original\"\n                      className=\"w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700\"\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Processed Image */}\n              {processedImageUrl && (\n                <div>\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300\">\n                      Processed Image\n                    </h4>\n                    <button\n                      onClick={downloadProcessedImage}\n                      className=\"inline-flex items-center px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors\"\n                    >\n                      <Download className=\"h-4 w-4 mr-1\" />\n                      Download\n                    </button>\n                  </div>\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden\">\n                    <img\n                      src={processedImageUrl}\n                      alt=\"Processed\"\n                      className=\"w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzD,WAAW;QACX,SAAS;QACT,cAAc;QACd,YAAY;IACd;IACA,2BAA2B;IAC3B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,YAAY;QACZ,cAAc;QACd,gBAAgB;IAClB;IAEA,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,WAAW,KAAK,KAAK,CAAC;gBAC5B,qBAAqB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,GAAG,SAAS,iBAAiB;oBAAC,CAAC;YAC1E,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;YAClD;QACF;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf;QACF;QACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;IAC7D,GAAG;QAAC;KAAkB;IAEtB,kBAAkB;IAClB,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa;YAAC;YAAc;YAAa;YAAa;YAAa;YAAc;SAAY;QACnG,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;QAEzC,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnC,SAAS;YACT,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa;YAAC;SAAa;QACjC,MAAM,UAAU,OAAO,MAAM,MAAM;QAEnC,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;YAClE,SAAS;YACT,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,CAAC;QACvB,SAAS;QACT,kBAAkB;QAClB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QAEjB,IAAI,kBAAkB,OAAO;YAC3B,aAAa;YACb,oBAAoB,IAAI,eAAe,CAAC;QAC1C;IACF;IAEA,wCAAwC;IACxC,MAAM,mBAAmB,CAAC;QACxB,SAAS;QACT,kBAAkB;QAClB,MAAM,aAAqB,EAAE;QAE7B,MAAM,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;YACxB,IAAI,kBAAkB,OAAO;gBAC3B,WAAW,IAAI,CAAC;YAClB;QACF;QAEA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,cAAc;YACd,kBAAkB,CAAC,IAAI,EAAE,WAAW,MAAM,CAAC,YAAY,CAAC;QAC1D;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,OAAO;QAC5B,SAAS;QACT,kBAAkB;QAClB,IAAI,iBAAiB,OAAO;YAC1B,YAAY;YACZ,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE;gBACnC,mBAAmB;gBACnB,kBAAkB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,uBAAuB,CAAC;YACrE,EAAE,OAAO,OAAO;gBACd,SAAS;YACX;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC,GAAoB;QAC1C,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,aAAa,CAAC,GAAoB;QACtC,EAAE,cAAc;QAChB,YAAY;QAEZ,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,IAAI,SAAS,SAAS;gBACpB,IAAI,aAAa,MAAM,MAAM,GAAG,GAAG;oBACjC,MAAM,WAAW,IAAI;oBACrB,MAAM,OAAO,CAAC,CAAA,OAAQ,SAAS,KAAK,CAAC,GAAG,CAAC;oBACzC,iBAAiB,SAAS,KAAK;gBACjC,OAAO;oBACL,gBAAgB,KAAK,CAAC,EAAE;gBAC1B;YACF,OAAO;gBACL,eAAe,KAAK,CAAC,EAAE;YACzB;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,CAAC,UAAU;YAC3B,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,oBAAoB;QACpB,sBAAsB;QACtB,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,qBAAqB;YACrB,sBAAsB;YAEtB,yBAAyB;YACzB,oBAAoB;YACpB,sBAAsB;YACtB,MAAM,cAAc,MAAM,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD,EAAE;YAEvC,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,MAAM,IAAI,MAAM;YAClB;YAEA,cAAc;YACd,oBAAoB;YACpB,sBAAsB;YACtB,MAAM,YAAY,MAAM,wHAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;YAEtD,UAAU;YACV,oBAAoB;YACpB,sBAAsB;YACtB,MAAM,cAAc,wHAAA,CAAA,kBAAe,CAAC,eAAe,CAAC,WAAW;YAE/D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,UAAU,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,MAAM,CAAC,QAAQ,CAAC;YAE/F,eAAe;YACf,mBAAmB;gBACjB,YAAY,UAAU,KAAK,CAAC,MAAM;gBAClC,cAAc,YAAY,MAAM;gBAChC,gBAAgB,KAAK,GAAG,KAAK;YAC/B;YAEA,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,SAAS,CAAC,oDAAoD,EAAE,UAAU,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO,UAAU,IAAI,CAAC,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC,CAAC;gBAC9I;YACF;YAEA,+BAA+B;YAC/B,oBAAoB;YACpB,sBAAsB;YACtB,MAAM,iBAAiB,CAAA,GAAA,8HAAA,CAAA,oBAAiB,AAAD;YACvC,MAAM,gBAAgB,MAAM,eAAe,mBAAmB,CAC5D,WACA,aACA;gBACE,WAAW,kBAAkB,YAAY,GAAG,YAAY,kBAAkB,SAAS;gBACnF,SAAS,kBAAkB,OAAO;gBAClC,YAAY,kBAAkB,UAAU;YAC1C;YAGF,iCAAiC;YACjC,sBAAsB;YACtB,MAAM,eAAe,IAAI,eAAe,CAAC;YACzC,qBAAqB;YACrB,iBAAiB;YAEjB,sBAAsB;YACtB,MAAM,YAAY,KAAK,GAAG,KAAK;YAC/B,mBAAmB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,gBAAgB;gBAAU,CAAC;YAClE,kBAAkB,CAAC,qBAAqB,EAAE,YAAY,MAAM,CAAC,WAAW,EAAE,YAAY,MAAM,GAAG,IAAI,OAAO,GAAG,mBAAmB,EAAE,CAAC,YAAU,IAAI,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;YACjK,oBAAoB;QAEtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,gBAAgB;YAChB,oBAAoB;QACtB;IACF;IAEA,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB,WAAW;YAC9B,MAAM,WAAW,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC;YAC3E,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;QACxB;IACF;IAEA,iBAAiB;IACjB,MAAM,WAAW;QACf,aAAa;QACb,cAAc,EAAE;QAChB,YAAY;QACZ,oBAAoB;QACpB,qBAAqB;QACrB,iBAAiB;QACjB,SAAS;QACT,kBAAkB;QAClB,mBAAmB,EAAE;QACrB,sBAAsB;QACtB,mBAAmB;YAAE,YAAY;YAAG,cAAc;YAAG,gBAAgB;QAAE;QACvE,aAAa;IACf;IAEA,mBAAmB;IACnB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,8CAA8C;YAC9C,IAAI,kBAAkB;gBACpB,IAAI,eAAe,CAAC;YACtB;YACA,IAAI,mBAAmB;gBACrB,IAAI,eAAe,CAAC;YACtB;QACA,oBAAoB;QACtB;IACF,GAAG;QAAC;QAAkB;KAAkB;IAExC,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,oCAAoC;YACpC,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,SAAS;gBACjD,IAAI,aAAa,YAAY,CAAC,cAAc;oBAC1C,EAAE,cAAc;oBAChB;gBACF;YACF;YAEA,2CAA2C;YAC3C,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,IAAI,eAAe;oBACjB,EAAE,cAAc;oBAChB;gBACF;YACF;YAEA,iCAAiC;YACjC,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,SAAS;gBACT,kBAAkB;YACpB;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;IACrD,GAAG;QAAC;QAAW;QAAU;QAAc;KAAc;IAErD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAM5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;;sDAEN,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAShD,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;0CAGN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyD;;;;;;kDAGvE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAiD;;;;;;kEAChE,8OAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;0DAEzB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAiD;;;;;;kEAChE,8OAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;0DAEzB,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAiD;;;;;;kEAChE,8OAAC;wDAAK,WAAU;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/B,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oMAAA,CAAA,QAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;wDAC9C,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;kDAK/D,8OAAC;wCACC,WAAW,CAAC,oEAAoE,EAC9E,aAAa,UACT,mDACA,8DACJ;wCACF,YAAY,CAAC,IAAM,eAAe,GAAG;wCACrC,aAAa;wCACb,QAAQ,CAAC,IAAM,WAAW,GAAG;;0DAE7B,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;0DAGrD,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,UAAU;gDACV,WAAU;gDACV,IAAG;gDACH,UAAU,CAAC;oDACT,IAAI,aAAa,EAAE,MAAM,CAAC,KAAK,EAAE;wDAC/B,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDACjC,OAAO;wDACL,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;wDAChC,IAAI,MAAM;4DACR,gBAAgB;wDAClB;oDACF;gDACF;;;;;;0DAEF,8OAAC;gDACC,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;;oCAIF,aAAa,CAAC,2BACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;gDAA6C;gDACrD,UAAU,IAAI;gDAAC;gDAAG,CAAC,UAAU,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;oCAKpE,aAAa,WAAW,MAAM,GAAG,mBAChC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;;oDAAkD;oDACtD,WAAW,MAAM;oDAAC;;;;;;;0DAE3B,8OAAC;gDAAI,WAAU;0DACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;wDAAgB,WAAU;;4DAAsD;4DAC5E,KAAK,IAAI;4DAAC;4DAAG,CAAC,KAAK,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;4DAAG;;uDAD7C;;;;;;;;;;;;;;;;;;;;;;0CAUpB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC;wCACC,WAAW,CAAC,oEAAoE,EAC9E,aAAa,SACT,mDACA,8DACJ;wCACF,YAAY,CAAC,IAAM,eAAe,GAAG;wCACrC,aAAa;wCACb,QAAQ,CAAC,IAAM,WAAW,GAAG;;0DAE7B,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;0DAGrD,8OAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,8OAAC;gDACC,MAAK;gDACL,QAAO;gDACP,WAAU;gDACV,IAAG;gDACH,UAAU,CAAC;oDACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oDAChC,IAAI,MAAM;wDACR,eAAe;oDACjB;gDACF;;;;;;0DAEF,8OAAC;gDACC,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;;oCAIF,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;;wDAA6C;wDACrD,SAAS,IAAI;wDAAC;wDAAG,CAAC,SAAS,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;4CAGzD,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAgD;4DAC1C,gBAAgB,MAAM;4DAAC;;;;;;;kEAE1C,8OAAC;wDAAI,WAAU;;4DACZ,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBACvC,8OAAC;oEAAgB,WAAU;;wEAAW;wEAAG;;mEAA/B;;;;;4DAEX,gBAAgB,MAAM,GAAG,oBACxB,8OAAC;gEAAI,WAAU;;oEAAuB;oEAAS,gBAAgB,MAAM,GAAG;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAW1F,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAyC;gCAChD;;;;;;;;;;;;oBAMT,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAA6C;gCACrD;;;;;;;;;;;;oBAMR,aAAa,0BACZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAIvE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAuD;;;;;;;;;;;;kDAIzE,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAK/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,OAAO,kBAAkB,SAAS;wDAClC,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACrF,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,OAAO,kBAAkB,SAAS;wDAClC,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACrF,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAKlB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;;oDAAkE;oDAC1E,kBAAkB,OAAO;oDAAC;;;;;;;0DAEnC,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,kBAAkB,OAAO;gDAChC,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDAC7F,WAAU;;;;;;;;;;;;kDAId,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,SAAS,kBAAkB,YAAY;wDACvC,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,cAAc,EAAE,MAAM,CAAC,OAAO;gEAAC,CAAC;wDAC1F,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAU;kEAA2C;;;;;;;;;;;;0DAI7D,8OAAC;gDAAE,WAAU;0DAAgD;;;;;;;;;;;;kDAK/D,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;;oDAAkE;oDAC1E,kBAAkB,UAAU;oDAAC;;;;;;;0DAEtC,8OAAC;gDACC,MAAK;gDACL,KAAI;gDACJ,KAAI;gDACJ,OAAO,kBAAkB,UAAU;gDACnC,UAAU,CAAC,IAAM,qBAAqB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDAChG,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,UAAU,CAAC,aAAa,CAAC,YAAY;gCACrC,SAAS;gCACT,WAAU;0CAET,6BACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAClB,oBAAoB;;mDAGvB;;;;;;4BAKH,8BACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAM;oDAAmB;;;;;;;;;;;;;kDAE5B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,mBAAmB,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;4BAOhD,gBAAgB,UAAU,GAAG,mBAC5B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAK;gDAAK,gBAAgB,UAAU;gDAAC;;;;;;;sDACtC,8OAAC;;gDAAK;gDAAI,gBAAgB,YAAY;gDAAC;;;;;;;sDACvC,8OAAC;;gDAAK;gDAAI,CAAC,gBAAgB,cAAc,GAAC,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;oBAOlE,CAAC,oBAAoB,iBAAiB,mBACrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,8OAAC;gCAAI,WAAU;;oCAEZ,kCACC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;oCAOjB,mCACC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAuD;;;;;;kEAGrE,8OAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIzC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC", "debugId": null}}]}