{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/app/api/ocr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// 百度OCR配置\nconst BAIDU_OCR_CONFIG = {\n  APP_ID: process.env.BAIDU_APP_ID || '',\n  API_KEY: process.env.BAIDU_API_KEY || '',\n  SECRET_KEY: process.env.BAIDU_SECRET_KEY || '',\n};\n\ninterface BaiduOCRWord {\n  words: string;\n  location: {\n    left: number;\n    top: number;\n    width: number;\n    height: number;\n  };\n}\n\ninterface BaiduOCRResponse {\n  words_result: BaiduOCRWord[];\n  words_result_num: number;\n}\n\n// 获取百度访问令牌\nasync function getBaiduAccessToken(): Promise<string> {\n  const tokenUrl = 'https://aip.baidubce.com/oauth/2.0/token';\n  const params = new URLSearchParams({\n    grant_type: 'client_credentials',\n    client_id: BAIDU_OCR_CONFIG.API_KEY,\n    client_secret: BAIDU_OCR_CONFIG.SECRET_KEY,\n  });\n\n  try {\n    const response = await fetch(`${tokenUrl}?${params}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    });\n\n    const data = await response.json();\n    \n    if (data.access_token) {\n      return data.access_token;\n    } else {\n      throw new Error('Failed to get access token: ' + JSON.stringify(data));\n    }\n  } catch (error) {\n    console.error('Error getting Baidu access token:', error);\n    throw new Error('Failed to authenticate with Baidu OCR');\n  }\n}\n\n// 调用百度OCR API\nasync function callBaiduOCR(imageBase64: string, accessToken: string): Promise<BaiduOCRResponse> {\n  const ocrUrl = 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic';\n  \n  const formData = new URLSearchParams({\n    image: imageBase64,\n    language_type: 'CHN_ENG', // 中英文混合识别\n  });\n\n  try {\n    const response = await fetch(`${ocrUrl}?access_token=${accessToken}`, {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n      body: formData,\n    });\n\n    const data = await response.json();\n    \n    if (data.error_code) {\n      throw new Error(`Baidu OCR API error: ${data.error_msg}`);\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error calling Baidu OCR:', error);\n    throw new Error('Failed to process image with Baidu OCR');\n  }\n}\n\n// 将图片文件转换为Base64\nfunction fileToBase64(file: File): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n      const result = reader.result as string;\n      // 移除data:image/...;base64,前缀\n      const base64 = result.split(',')[1];\n      resolve(base64);\n    };\n    reader.onerror = reject;\n    reader.readAsDataURL(file);\n  });\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    // 检查环境变量\n    if (!BAIDU_OCR_CONFIG.API_KEY || !BAIDU_OCR_CONFIG.SECRET_KEY) {\n      return NextResponse.json(\n        { error: 'Baidu OCR credentials not configured' },\n        { status: 500 }\n      );\n    }\n\n    const formData = await request.formData();\n    const imageFile = formData.get('image') as File;\n    \n    if (!imageFile) {\n      return NextResponse.json(\n        { error: 'No image file provided' },\n        { status: 400 }\n      );\n    }\n\n    // 验证文件类型\n    if (!imageFile.type.startsWith('image/')) {\n      return NextResponse.json(\n        { error: 'Invalid file type. Please upload an image.' },\n        { status: 400 }\n      );\n    }\n\n    // 验证文件大小 (百度OCR限制4MB)\n    if (imageFile.size > 4 * 1024 * 1024) {\n      return NextResponse.json(\n        { error: 'Image file too large. Maximum size is 4MB.' },\n        { status: 400 }\n      );\n    }\n\n    // 转换为Base64\n    const imageBuffer = await imageFile.arrayBuffer();\n    const imageBase64 = Buffer.from(imageBuffer).toString('base64');\n\n    // 获取访问令牌\n    const accessToken = await getBaiduAccessToken();\n\n    // 调用百度OCR\n    const ocrResult = await callBaiduOCR(imageBase64, accessToken);\n\n    // 转换为统一格式\n    const result = {\n      text: ocrResult.words_result.map(item => item.words).join(' '),\n      confidence: 95, // 百度OCR通常有很高的准确率\n      words: ocrResult.words_result.map(item => ({\n        text: item.words || '',\n        confidence: 95,\n        bbox: {\n          x0: item.location?.left || 0,\n          y0: item.location?.top || 0,\n          x1: (item.location?.left || 0) + (item.location?.width || 0),\n          y1: (item.location?.top || 0) + (item.location?.height || 0),\n        }\n      }))\n    };\n\n    return NextResponse.json(result);\n\n  } catch (error) {\n    console.error('OCR processing error:', error);\n    return NextResponse.json(\n      { error: error instanceof Error ? error.message : 'OCR processing failed' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,UAAU;AACV,MAAM,mBAAmB;IACvB,QAAQ,QAAQ,GAAG,CAAC,YAAY,IAAI;IACpC,SAAS,QAAQ,GAAG,CAAC,aAAa,IAAI;IACtC,YAAY,QAAQ,GAAG,CAAC,gBAAgB,IAAI;AAC9C;AAiBA,WAAW;AACX,eAAe;IACb,MAAM,WAAW;IACjB,MAAM,SAAS,IAAI,gBAAgB;QACjC,YAAY;QACZ,WAAW,iBAAiB,OAAO;QACnC,eAAe,iBAAiB,UAAU;IAC5C;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,SAAS,CAAC,EAAE,QAAQ,EAAE;YACpD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,YAAY,EAAE;YACrB,OAAO,KAAK,YAAY;QAC1B,OAAO;YACL,MAAM,IAAI,MAAM,iCAAiC,KAAK,SAAS,CAAC;QAClE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,cAAc;AACd,eAAe,aAAa,WAAmB,EAAE,WAAmB;IAClE,MAAM,SAAS;IAEf,MAAM,WAAW,IAAI,gBAAgB;QACnC,OAAO;QACP,eAAe;IACjB;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,OAAO,cAAc,EAAE,aAAa,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM;QACR;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,IAAI,KAAK,UAAU,EAAE;YACnB,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,KAAK,SAAS,EAAE;QAC1D;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM,IAAI,MAAM;IAClB;AACF;AAEA,iBAAiB;AACjB,SAAS,aAAa,IAAU;IAC9B,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,GAAG;YACd,MAAM,SAAS,OAAO,MAAM;YAC5B,6BAA6B;YAC7B,MAAM,SAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;YACnC,QAAQ;QACV;QACA,OAAO,OAAO,GAAG;QACjB,OAAO,aAAa,CAAC;IACvB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,SAAS;QACT,IAAI,CAAC,iBAAiB,OAAO,IAAI,CAAC,iBAAiB,UAAU,EAAE;YAC7D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuC,GAChD;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,YAAY,SAAS,GAAG,CAAC;QAE/B,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6C,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,IAAI,UAAU,IAAI,GAAG,IAAI,OAAO,MAAM;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6C,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,MAAM,cAAc,MAAM,UAAU,WAAW;QAC/C,MAAM,cAAc,OAAO,IAAI,CAAC,aAAa,QAAQ,CAAC;QAEtD,SAAS;QACT,MAAM,cAAc,MAAM;QAE1B,UAAU;QACV,MAAM,YAAY,MAAM,aAAa,aAAa;QAElD,UAAU;QACV,MAAM,SAAS;YACb,MAAM,UAAU,YAAY,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,EAAE,IAAI,CAAC;YAC1D,YAAY;YACZ,OAAO,UAAU,YAAY,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACzC,MAAM,KAAK,KAAK,IAAI;oBACpB,YAAY;oBACZ,MAAM;wBACJ,IAAI,KAAK,QAAQ,EAAE,QAAQ;wBAC3B,IAAI,KAAK,QAAQ,EAAE,OAAO;wBAC1B,IAAI,CAAC,KAAK,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,SAAS,CAAC;wBAC3D,IAAI,CAAC,KAAK,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,UAAU,CAAC;oBAC7D;gBACF,CAAC;QACH;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAAwB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}