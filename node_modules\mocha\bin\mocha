#!/usr/bin/env node

'use strict';

/**
 * This tiny wrapper file checks for known node flags and appends them
 * when found, before invoking the "real" _mocha(1) executable.
 */

const spawn = require('child_process').spawn;
const path = require('path');
const getOptions = require('./options');
const args = [path.join(__dirname, '_mocha')];

// Load mocha.opts into process.argv
// Must be loaded here to handle node-specific options
getOptions();

process.argv.slice(2).forEach(arg => {
  const flag = arg.split('=')[0];

  switch (flag) {
    case '-d':
      args.unshift('--debug');
      args.push('--no-timeouts');
      break;
    case 'debug':
    case '--debug':
    case '--debug-brk':
    case '--inspect':
    case '--inspect-brk':
      args.unshift(arg);
      args.push('--no-timeouts');
      break;
    case '-gc':
    case '--expose-gc':
      args.unshift('--expose-gc');
      break;
    case '--gc-global':
    case '--es_staging':
    case '--no-deprecation':
    case '--no-warnings':
    case '--prof':
    case '--log-timer-events':
    case '--throw-deprecation':
    case '--trace-deprecation':
    case '--trace-warnings':
    case '--use_strict':
    case '--allow-natives-syntax':
    case '--perf-basic-prof':
    case '--napi-modules':
      args.unshift(arg);
      break;
    default:
      if (arg.indexOf('--harmony') === 0) {
        args.unshift(arg);
      } else if (arg.indexOf('--trace') === 0) {
        args.unshift(arg);
      } else if (arg.indexOf('--icu-data-dir') === 0) {
        args.unshift(arg);
      } else if (arg.indexOf('--max-old-space-size') === 0) {
        args.unshift(arg);
      } else if (arg.indexOf('--preserve-symlinks') === 0) {
        args.unshift(arg);
      } else {
        args.push(arg);
      }
      break;
  }
});

const proc = spawn(process.execPath, args, {
  stdio: 'inherit'
});
proc.on('exit', (code, signal) => {
  process.on('exit', () => {
    if (signal) {
      process.kill(process.pid, signal);
    } else {
      process.exit(code);
    }
  });
});

// terminate children.
process.on('SIGINT', () => {
  proc.kill('SIGINT'); // calls runner.abort()
  proc.kill('SIGTERM'); // if that didn't work, we're probably in an infinite loop, so make it die.
});
