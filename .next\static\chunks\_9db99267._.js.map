{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/utils/ocr.ts"], "sourcesContent": ["import { create<PERSON><PERSON><PERSON>, Worker } from 'tesseract.js';\n\nexport interface OCRResult {\n  text: string;\n  confidence: number;\n  words: Array<{\n    text: string;\n    confidence: number;\n    bbox: {\n      x0: number;\n      y0: number;\n      x1: number;\n      y1: number;\n    };\n  }>;\n}\n\nexport interface TextMatch {\n  text: string;\n  bbox: {\n    x0: number;\n    y0: number;\n    x1: number;\n    y1: number;\n  };\n  confidence: number;\n}\n\nclass OCRService {\n  private worker: Worker | null = null;\n  private isInitialized = false;\n\n  async initialize(): Promise<void> {\n    if (this.isInitialized) return;\n\n    try {\n      this.worker = await createWorker('eng');\n      this.isInitialized = true;\n    } catch (error) {\n      console.error('Failed to initialize OCR worker:', error);\n      throw new Error('Failed to initialize OCR service');\n    }\n  }\n\n  async recognizeText(imageFile: File): Promise<OCRResult> {\n    if (!this.worker || !this.isInitialized) {\n      await this.initialize();\n    }\n\n    if (!this.worker) {\n      throw new Error('OCR worker not initialized');\n    }\n\n    try {\n      const { data } = await this.worker.recognize(imageFile);\n\n      return {\n        text: data.text || '',\n        confidence: data.confidence || 0,\n        words: (data.words || []).map(word => ({\n          text: word.text || '',\n          confidence: word.confidence || 0,\n          bbox: word.bbox || { x0: 0, y0: 0, x1: 0, y1: 0 }\n        }))\n      };\n    } catch (error) {\n      console.error('OCR recognition failed:', error);\n      throw new Error('Failed to recognize text in image');\n    }\n  }\n\n  findTextMatches(ocrResult: OCRResult, targetTexts: string[]): TextMatch[] {\n    const matches: TextMatch[] = [];\n    const normalizedTargets = targetTexts.map(text => text.toLowerCase().trim());\n\n    // Check each word from OCR results\n    ocrResult.words.forEach(word => {\n      const normalizedWord = word.text.toLowerCase().trim();\n      \n      // Direct word match\n      if (normalizedTargets.includes(normalizedWord)) {\n        matches.push({\n          text: word.text,\n          bbox: word.bbox,\n          confidence: word.confidence\n        });\n      }\n      \n      // Partial match (for phrases)\n      normalizedTargets.forEach(target => {\n        if (target.includes(' ')) { // Multi-word phrase\n          // This is a simplified approach - in a real implementation,\n          // you'd want to group consecutive words and check for phrase matches\n          if (normalizedWord.includes(target.split(' ')[0])) {\n            matches.push({\n              text: word.text,\n              bbox: word.bbox,\n              confidence: word.confidence\n            });\n          }\n        }\n      });\n    });\n\n    return matches;\n  }\n\n  async terminate(): Promise<void> {\n    if (this.worker) {\n      await this.worker.terminate();\n      this.worker = null;\n      this.isInitialized = false;\n    }\n  }\n}\n\n// Export a singleton instance\nexport const ocrService = new OCRService();\n\n// Utility function to read text file content\nexport async function readTextFile(file: File): Promise<string[]> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    \n    reader.onload = (event) => {\n      const content = event.target?.result as string;\n      const lines = content\n        .split('\\n')\n        .map(line => line.trim())\n        .filter(line => line.length > 0);\n      resolve(lines);\n    };\n    \n    reader.onerror = () => {\n      reject(new Error('Failed to read text file'));\n    };\n    \n    reader.readAsText(file);\n  });\n}\n"], "names": [], "mappings": ";;;;AAAA;;AA4BA,MAAM;IACI,SAAwB,KAAK;IAC7B,gBAAgB,MAAM;IAE9B,MAAM,aAA4B;QAChC,IAAI,IAAI,CAAC,aAAa,EAAE;QAExB,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA,GAAA,kJAAA,CAAA,eAAY,AAAD,EAAE;YACjC,IAAI,CAAC,aAAa,GAAG;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,cAAc,SAAe,EAAsB;QACvD,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACvC,MAAM,IAAI,CAAC,UAAU;QACvB;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;YAE7C,OAAO;gBACL,MAAM,KAAK,IAAI,IAAI;gBACnB,YAAY,KAAK,UAAU,IAAI;gBAC/B,OAAO,CAAC,KAAK,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAA,OAAQ,CAAC;wBACrC,MAAM,KAAK,IAAI,IAAI;wBACnB,YAAY,KAAK,UAAU,IAAI;wBAC/B,MAAM,KAAK,IAAI,IAAI;4BAAE,IAAI;4BAAG,IAAI;4BAAG,IAAI;4BAAG,IAAI;wBAAE;oBAClD,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,gBAAgB,SAAoB,EAAE,WAAqB,EAAe;QACxE,MAAM,UAAuB,EAAE;QAC/B,MAAM,oBAAoB,YAAY,GAAG,CAAC,CAAA,OAAQ,KAAK,WAAW,GAAG,IAAI;QAEzE,mCAAmC;QACnC,UAAU,KAAK,CAAC,OAAO,CAAC,CAAA;YACtB,MAAM,iBAAiB,KAAK,IAAI,CAAC,WAAW,GAAG,IAAI;YAEnD,oBAAoB;YACpB,IAAI,kBAAkB,QAAQ,CAAC,iBAAiB;gBAC9C,QAAQ,IAAI,CAAC;oBACX,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,YAAY,KAAK,UAAU;gBAC7B;YACF;YAEA,8BAA8B;YAC9B,kBAAkB,OAAO,CAAC,CAAA;gBACxB,IAAI,OAAO,QAAQ,CAAC,MAAM;oBACxB,4DAA4D;oBAC5D,qEAAqE;oBACrE,IAAI,eAAe,QAAQ,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;wBACjD,QAAQ,IAAI,CAAC;4BACX,MAAM,KAAK,IAAI;4BACf,MAAM,KAAK,IAAI;4BACf,YAAY,KAAK,UAAU;wBAC7B;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,YAA2B;QAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS;YAC3B,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;AACF;AAGO,MAAM,aAAa,IAAI;AAGvB,eAAe,aAAa,IAAU;IAC3C,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI;QAEnB,OAAO,MAAM,GAAG,CAAC;YACf,MAAM,UAAU,MAAM,MAAM,EAAE;YAC9B,MAAM,QAAQ,QACX,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IACrB,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG;YAChC,QAAQ;QACV;QAEA,OAAO,OAAO,GAAG;YACf,OAAO,IAAI,MAAM;QACnB;QAEA,OAAO,UAAU,CAAC;IACpB;AACF", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/utils/imageProcessor.ts"], "sourcesContent": ["import { TextMatch } from './ocr';\n\nexport interface ProcessingOptions {\n  fillColor?: string;\n  padding?: number;\n  blurRadius?: number;\n}\n\nexport class ImageProcessor {\n  private canvas: HTMLCanvasElement | null = null;\n  private ctx: CanvasRenderingContext2D | null = null;\n\n  private initializeCanvas() {\n    if (typeof window === 'undefined') {\n      throw new Error('ImageProcessor can only be used in the browser');\n    }\n\n    if (!this.canvas) {\n      this.canvas = document.createElement('canvas');\n      const context = this.canvas.getContext('2d');\n      if (!context) {\n        throw new Error('Failed to get canvas 2D context');\n      }\n      this.ctx = context;\n    }\n  }\n\n  async loadImage(file: File): Promise<HTMLImageElement> {\n    return new Promise((resolve, reject) => {\n      const img = new Image();\n      const url = URL.createObjectURL(file);\n      \n      img.onload = () => {\n        URL.revokeObjectURL(url);\n        resolve(img);\n      };\n      \n      img.onerror = () => {\n        URL.revokeObjectURL(url);\n        reject(new Error('Failed to load image'));\n      };\n      \n      img.src = url;\n    });\n  }\n\n  async removeTextFromImage(\n    imageFile: File,\n    textMatches: TextMatch[],\n    options: ProcessingOptions = {}\n  ): Promise<Blob> {\n    this.initializeCanvas();\n    if (!this.canvas || !this.ctx) {\n      throw new Error('Canvas not initialized');\n    }\n\n    const img = await this.loadImage(imageFile);\n\n    // Set canvas dimensions to match image\n    this.canvas.width = img.width;\n    this.canvas.height = img.height;\n\n    // Draw the original image\n    this.ctx.drawImage(img, 0, 0);\n\n    // Remove text by covering matched areas\n    await this.removeTextAreas(textMatches, options);\n\n    return new Promise((resolve, reject) => {\n      this.canvas!.toBlob((blob) => {\n        if (blob) {\n          resolve(blob);\n        } else {\n          reject(new Error('Failed to create processed image blob'));\n        }\n      }, 'image/png');\n    });\n  }\n\n  private async removeTextAreas(textMatches: TextMatch[], options: ProcessingOptions): Promise<void> {\n    const { fillColor = '#FFFFFF', padding = 2, blurRadius = 0 } = options;\n    \n    for (const match of textMatches) {\n      const { bbox } = match;\n      \n      // Add padding to the bounding box\n      const x = Math.max(0, bbox.x0 - padding);\n      const y = Math.max(0, bbox.y0 - padding);\n      const width = Math.min(this.canvas.width - x, bbox.x1 - bbox.x0 + 2 * padding);\n      const height = Math.min(this.canvas.height - y, bbox.y1 - bbox.y0 + 2 * padding);\n      \n      if (blurRadius > 0) {\n        // Apply blur effect for more natural text removal\n        await this.applyInpainting(x, y, width, height);\n      } else {\n        // Simple fill with solid color\n        this.ctx.fillStyle = fillColor;\n        this.ctx.fillRect(x, y, width, height);\n      }\n    }\n  }\n\n  private async applyInpainting(x: number, y: number, width: number, height: number): Promise<void> {\n    // Get surrounding pixels for color sampling\n    const surroundingData = this.getSurroundingPixels(x, y, width, height);\n    \n    // Calculate average color from surrounding pixels\n    const avgColor = this.calculateAverageColor(surroundingData);\n    \n    // Create gradient fill for more natural appearance\n    const gradient = this.ctx.createLinearGradient(x, y, x + width, y + height);\n    gradient.addColorStop(0, avgColor);\n    gradient.addColorStop(1, this.adjustBrightness(avgColor, 0.9));\n    \n    this.ctx.fillStyle = gradient;\n    this.ctx.fillRect(x, y, width, height);\n    \n    // Apply slight blur effect\n    this.applyGaussianBlur(x, y, width, height, 1);\n  }\n\n  private getSurroundingPixels(x: number, y: number, width: number, height: number): ImageData {\n    const margin = 5;\n    const surroundX = Math.max(0, x - margin);\n    const surroundY = Math.max(0, y - margin);\n    const surroundWidth = Math.min(this.canvas.width - surroundX, width + 2 * margin);\n    const surroundHeight = Math.min(this.canvas.height - surroundY, height + 2 * margin);\n    \n    return this.ctx.getImageData(surroundX, surroundY, surroundWidth, surroundHeight);\n  }\n\n  private calculateAverageColor(imageData: ImageData): string {\n    const data = imageData.data;\n    let r = 0, g = 0, b = 0;\n    let pixelCount = 0;\n    \n    for (let i = 0; i < data.length; i += 4) {\n      r += data[i];\n      g += data[i + 1];\n      b += data[i + 2];\n      pixelCount++;\n    }\n    \n    r = Math.round(r / pixelCount);\n    g = Math.round(g / pixelCount);\n    b = Math.round(b / pixelCount);\n    \n    return `rgb(${r}, ${g}, ${b})`;\n  }\n\n  private adjustBrightness(color: string, factor: number): string {\n    const rgb = color.match(/\\d+/g);\n    if (!rgb) return color;\n    \n    const r = Math.round(parseInt(rgb[0]) * factor);\n    const g = Math.round(parseInt(rgb[1]) * factor);\n    const b = Math.round(parseInt(rgb[2]) * factor);\n    \n    return `rgb(${r}, ${g}, ${b})`;\n  }\n\n  private applyGaussianBlur(x: number, y: number, width: number, height: number, radius: number): void {\n    // Simple box blur approximation of Gaussian blur\n    const imageData = this.ctx.getImageData(x, y, width, height);\n    const blurred = this.boxBlur(imageData, radius);\n    this.ctx.putImageData(blurred, x, y);\n  }\n\n  private boxBlur(imageData: ImageData, radius: number): ImageData {\n    const data = new Uint8ClampedArray(imageData.data);\n    const width = imageData.width;\n    const height = imageData.height;\n    \n    for (let y = 0; y < height; y++) {\n      for (let x = 0; x < width; x++) {\n        let r = 0, g = 0, b = 0, a = 0;\n        let count = 0;\n        \n        for (let dy = -radius; dy <= radius; dy++) {\n          for (let dx = -radius; dx <= radius; dx++) {\n            const nx = x + dx;\n            const ny = y + dy;\n            \n            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {\n              const idx = (ny * width + nx) * 4;\n              r += imageData.data[idx];\n              g += imageData.data[idx + 1];\n              b += imageData.data[idx + 2];\n              a += imageData.data[idx + 3];\n              count++;\n            }\n          }\n        }\n        \n        const idx = (y * width + x) * 4;\n        data[idx] = r / count;\n        data[idx + 1] = g / count;\n        data[idx + 2] = b / count;\n        data[idx + 3] = a / count;\n      }\n    }\n    \n    return new ImageData(data, width, height);\n  }\n\n  getCanvas(): HTMLCanvasElement {\n    return this.canvas;\n  }\n}\n\n// Create a function to get the image processor instance (lazy initialization)\nlet imageProcessorInstance: ImageProcessor | null = null;\n\nexport function getImageProcessor(): ImageProcessor {\n  if (!imageProcessorInstance) {\n    imageProcessorInstance = new ImageProcessor();\n  }\n  return imageProcessorInstance;\n}\n"], "names": [], "mappings": ";;;;AAQO,MAAM;IACH,SAAmC,KAAK;IACxC,MAAuC,KAAK;IAE5C,mBAAmB;QACzB,uCAAmC;;QAEnC;QAEA,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,SAAS,aAAa,CAAC;YACrC,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YACvC,IAAI,CAAC,SAAS;gBACZ,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,GAAG,GAAG;QACb;IACF;IAEA,MAAM,UAAU,IAAU,EAA6B;QACrD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,MAAM,IAAI;YAChB,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,IAAI,MAAM,GAAG;gBACX,IAAI,eAAe,CAAC;gBACpB,QAAQ;YACV;YAEA,IAAI,OAAO,GAAG;gBACZ,IAAI,eAAe,CAAC;gBACpB,OAAO,IAAI,MAAM;YACnB;YAEA,IAAI,GAAG,GAAG;QACZ;IACF;IAEA,MAAM,oBACJ,SAAe,EACf,WAAwB,EACxB,UAA6B,CAAC,CAAC,EAChB;QACf,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YAC7B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC;QAEjC,uCAAuC;QACvC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,KAAK;QAC7B,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM;QAE/B,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,GAAG;QAE3B,wCAAwC;QACxC,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa;QAExC,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,MAAM,CAAE,MAAM,CAAC,CAAC;gBACnB,IAAI,MAAM;oBACR,QAAQ;gBACV,OAAO;oBACL,OAAO,IAAI,MAAM;gBACnB;YACF,GAAG;QACL;IACF;IAEA,MAAc,gBAAgB,WAAwB,EAAE,OAA0B,EAAiB;QACjG,MAAM,EAAE,YAAY,SAAS,EAAE,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,GAAG;QAE/D,KAAK,MAAM,SAAS,YAAa;YAC/B,MAAM,EAAE,IAAI,EAAE,GAAG;YAEjB,kCAAkC;YAClC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG;YAChC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,EAAE,GAAG;YAChC,MAAM,QAAQ,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI;YACtE,MAAM,SAAS,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI;YAExE,IAAI,aAAa,GAAG;gBAClB,kDAAkD;gBAClD,MAAM,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,OAAO;YAC1C,OAAO;gBACL,+BAA+B;gBAC/B,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG;gBACrB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO;YACjC;QACF;IACF;IAEA,MAAc,gBAAgB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAiB;QAChG,4CAA4C;QAC5C,MAAM,kBAAkB,IAAI,CAAC,oBAAoB,CAAC,GAAG,GAAG,OAAO;QAE/D,kDAAkD;QAClD,MAAM,WAAW,IAAI,CAAC,qBAAqB,CAAC;QAE5C,mDAAmD;QACnD,MAAM,WAAW,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,GAAG,IAAI,OAAO,IAAI;QACpE,SAAS,YAAY,CAAC,GAAG;QACzB,SAAS,YAAY,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAEzD,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG;QACrB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO;QAE/B,2BAA2B;QAC3B,IAAI,CAAC,iBAAiB,CAAC,GAAG,GAAG,OAAO,QAAQ;IAC9C;IAEQ,qBAAqB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAa;QAC3F,MAAM,SAAS;QACf,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI;QAClC,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI;QAClC,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,WAAW,QAAQ,IAAI;QAC1E,MAAM,iBAAiB,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,WAAW,SAAS,IAAI;QAE7E,OAAO,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,WAAW,eAAe;IACpE;IAEQ,sBAAsB,SAAoB,EAAU;QAC1D,MAAM,OAAO,UAAU,IAAI;QAC3B,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI;QACtB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACvC,KAAK,IAAI,CAAC,EAAE;YACZ,KAAK,IAAI,CAAC,IAAI,EAAE;YAChB,KAAK,IAAI,CAAC,IAAI,EAAE;YAChB;QACF;QAEA,IAAI,KAAK,KAAK,CAAC,IAAI;QACnB,IAAI,KAAK,KAAK,CAAC,IAAI;QACnB,IAAI,KAAK,KAAK,CAAC,IAAI;QAEnB,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC;IAEQ,iBAAiB,KAAa,EAAE,MAAc,EAAU;QAC9D,MAAM,MAAM,MAAM,KAAK,CAAC;QACxB,IAAI,CAAC,KAAK,OAAO;QAEjB,MAAM,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI;QACxC,MAAM,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI;QACxC,MAAM,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI;QAExC,OAAO,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;IAChC;IAEQ,kBAAkB,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,MAAc,EAAE,MAAc,EAAQ;QACnG,iDAAiD;QACjD,MAAM,YAAY,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,GAAG,OAAO;QACrD,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,WAAW;QACxC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,SAAS,GAAG;IACpC;IAEQ,QAAQ,SAAoB,EAAE,MAAc,EAAa;QAC/D,MAAM,OAAO,IAAI,kBAAkB,UAAU,IAAI;QACjD,MAAM,QAAQ,UAAU,KAAK;QAC7B,MAAM,SAAS,UAAU,MAAM;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;YAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI;gBAC7B,IAAI,QAAQ;gBAEZ,IAAK,IAAI,KAAK,CAAC,QAAQ,MAAM,QAAQ,KAAM;oBACzC,IAAK,IAAI,KAAK,CAAC,QAAQ,MAAM,QAAQ,KAAM;wBACzC,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBAEf,IAAI,MAAM,KAAK,KAAK,SAAS,MAAM,KAAK,KAAK,QAAQ;4BACnD,MAAM,MAAM,CAAC,KAAK,QAAQ,EAAE,IAAI;4BAChC,KAAK,UAAU,IAAI,CAAC,IAAI;4BACxB,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BAC5B,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BAC5B,KAAK,UAAU,IAAI,CAAC,MAAM,EAAE;4BAC5B;wBACF;oBACF;gBACF;gBAEA,MAAM,MAAM,CAAC,IAAI,QAAQ,CAAC,IAAI;gBAC9B,IAAI,CAAC,IAAI,GAAG,IAAI;gBAChB,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;gBACpB,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;gBACpB,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;YACtB;QACF;QAEA,OAAO,IAAI,UAAU,MAAM,OAAO;IACpC;IAEA,YAA+B;QAC7B,OAAO,IAAI,CAAC,MAAM;IACpB;AACF;AAEA,8EAA8E;AAC9E,IAAI,yBAAgD;AAE7C,SAAS;IACd,IAAI,CAAC,wBAAwB;QAC3B,yBAAyB,IAAI;IAC/B;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Upload, FileText, Image as ImageIcon, Download, Loader2 } from 'lucide-react';\nimport { ocrService, readTextFile } from '@/utils/ocr';\nimport { getImageProcessor } from '@/utils/imageProcessor';\nimport { saveAs } from 'file-saver';\n\nexport default function Home() {\n  const [imageFile, setImageFile] = useState<File | null>(null);\n  const [textFile, setTextFile] = useState<File | null>(null);\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [originalImageUrl, setOriginalImageUrl] = useState<string>('');\n  const [processedImageUrl, setProcessedImageUrl] = useState<string>('');\n  const [dragOver, setDragOver] = useState<'image' | 'text' | null>(null);\n  const [error, setError] = useState<string>('');\n  const [processingStatus, setProcessingStatus] = useState<string>('');\n  const [processedBlob, setProcessedBlob] = useState<Blob | null>(null);\n  const [successMessage, setSuccessMessage] = useState<string>('');\n  const [textFileContent, setTextFileContent] = useState<string[]>([]);\n\n  // File validation\n  const validateImageFile = (file: File): boolean => {\n    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp'];\n    const maxSize = 10 * 1024 * 1024; // 10MB\n\n    if (!validTypes.includes(file.type)) {\n      setError('Please upload a valid image file (JPG, PNG, GIF, WebP, BMP)');\n      return false;\n    }\n\n    if (file.size > maxSize) {\n      setError('Image file size must be less than 10MB');\n      return false;\n    }\n\n    return true;\n  };\n\n  const validateTextFile = (file: File): boolean => {\n    const validTypes = ['text/plain'];\n    const maxSize = 1024 * 1024; // 1MB\n\n    if (!validTypes.includes(file.type) && !file.name.endsWith('.txt')) {\n      setError('Please upload a valid text file (.txt)');\n      return false;\n    }\n\n    if (file.size > maxSize) {\n      setError('Text file size must be less than 1MB');\n      return false;\n    }\n\n    return true;\n  };\n\n  // Handle image file selection\n  const handleImageFile = (file: File) => {\n    setError('');\n    setSuccessMessage('');\n    // Clear previous results\n    setProcessedImageUrl('');\n    setProcessedBlob(null);\n\n    if (validateImageFile(file)) {\n      setImageFile(file);\n      setOriginalImageUrl(URL.createObjectURL(file));\n    }\n  };\n\n  // Handle text file selection\n  const handleTextFile = async (file: File) => {\n    setError('');\n    setSuccessMessage('');\n    if (validateTextFile(file)) {\n      setTextFile(file);\n      try {\n        const content = await readTextFile(file);\n        setTextFileContent(content);\n        setSuccessMessage(`Loaded ${content.length} text entries to remove`);\n      } catch (error) {\n        setError('Failed to read text file content');\n      }\n    }\n  };\n\n  // Drag and drop handlers\n  const handleDragOver = (e: React.DragEvent, type: 'image' | 'text') => {\n    e.preventDefault();\n    setDragOver(type);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setDragOver(null);\n  };\n\n  const handleDrop = (e: React.DragEvent, type: 'image' | 'text') => {\n    e.preventDefault();\n    setDragOver(null);\n\n    const files = Array.from(e.dataTransfer.files);\n    if (files.length > 0) {\n      const file = files[0];\n      if (type === 'image') {\n        handleImageFile(file);\n      } else {\n        handleTextFile(file);\n      }\n    }\n  };\n\n  // Main processing function\n  const processImage = async () => {\n    if (!imageFile || !textFile) {\n      setError('Please upload both an image and text file');\n      return;\n    }\n\n    setIsProcessing(true);\n    setError('');\n    setProcessingStatus('Initializing OCR...');\n\n    try {\n      // Initialize OCR service\n      await ocrService.initialize();\n\n      // Read text file content\n      setProcessingStatus('Reading text file...');\n      const targetTexts = await readTextFile(textFile);\n\n      if (targetTexts.length === 0) {\n        throw new Error('Text file is empty or contains no valid text');\n      }\n\n      // Perform OCR on the image\n      setProcessingStatus('Analyzing image text...');\n      const ocrResult = await ocrService.recognizeText(imageFile);\n\n      // Find matching text\n      setProcessingStatus('Finding text matches...');\n      const textMatches = ocrService.findTextMatches(ocrResult, targetTexts);\n\n      console.log(`OCR detected ${ocrResult.words.length} words, found ${textMatches.length} matches`);\n\n      if (textMatches.length === 0) {\n        setError(`No matching text found in the image. OCR detected: \"${ocrResult.text.substring(0, 100)}${ocrResult.text.length > 100 ? '...' : ''}\"`);\n        return;\n      }\n\n      // Process image to remove text\n      setProcessingStatus('Removing text from image...');\n      const imageProcessor = getImageProcessor();\n      const processedBlob = await imageProcessor.removeTextFromImage(\n        imageFile,\n        textMatches,\n        { fillColor: '#FFFFFF', padding: 3 }\n      );\n\n      // Create URL for processed image\n      const processedUrl = URL.createObjectURL(processedBlob);\n      setProcessedImageUrl(processedUrl);\n      setProcessedBlob(processedBlob);\n\n      setSuccessMessage(`Successfully removed ${textMatches.length} text match${textMatches.length > 1 ? 'es' : ''} from the image!`);\n      setProcessingStatus('');\n\n    } catch (error) {\n      console.error('Processing error:', error);\n      setError(error instanceof Error ? error.message : 'An error occurred during processing');\n    } finally {\n      setIsProcessing(false);\n      setProcessingStatus('');\n    }\n  };\n\n  // Download processed image\n  const downloadProcessedImage = () => {\n    if (processedBlob && imageFile) {\n      const fileName = `processed_${imageFile.name.replace(/\\.[^/.]+$/, '')}.png`;\n      saveAs(processedBlob, fileName);\n    }\n  };\n\n  // Cleanup function\n  useEffect(() => {\n    return () => {\n      // Cleanup object URLs to prevent memory leaks\n      if (originalImageUrl) {\n        URL.revokeObjectURL(originalImageUrl);\n      }\n      if (processedImageUrl) {\n        URL.revokeObjectURL(processedImageUrl);\n      }\n      // Terminate OCR worker\n      ocrService.terminate();\n    };\n  }, [originalImageUrl, processedImageUrl]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"bg-blue-600 p-2 rounded-lg\">\n                <ImageIcon className=\"h-6 w-6 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Text Remover\n                </h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                  Remove specific text from images using OCR\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Instructions */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 mb-8\">\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n            How to use this tool:\n          </h2>\n          <ol className=\"list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-400\">\n            <li>Upload an image file (JPG, PNG, GIF, etc.)</li>\n            <li>Upload a text file containing words/phrases to remove</li>\n            <li>Click \"Process Image\" to detect and remove matching text</li>\n            <li>Download the processed image with text removed</li>\n          </ol>\n        </div>\n\n        {/* File Upload Section */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\n          {/* Image Upload */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center\">\n              <ImageIcon className=\"h-5 w-5 mr-2\" />\n              Upload Image\n            </h3>\n            <div\n              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n                dragOver === 'image'\n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'\n              }`}\n              onDragOver={(e) => handleDragOver(e, 'image')}\n              onDragLeave={handleDragLeave}\n              onDrop={(e) => handleDrop(e, 'image')}\n            >\n              <Upload className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400 mb-2\">\n                Drag and drop your image here, or click to browse\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n                Supports JPG, PNG, GIF, WebP, BMP (max 10MB)\n              </p>\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                className=\"hidden\"\n                id=\"image-upload\"\n                onChange={(e) => {\n                  const file = e.target.files?.[0];\n                  if (file) {\n                    handleImageFile(file);\n                  }\n                }}\n              />\n              <label\n                htmlFor=\"image-upload\"\n                className=\"mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors\"\n              >\n                Choose Image\n              </label>\n            </div>\n            {imageFile && (\n              <div className=\"mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md\">\n                <p className=\"text-sm text-green-800 dark:text-green-400\">\n                  ✓ {imageFile.name} ({(imageFile.size / 1024 / 1024).toFixed(2)} MB)\n                </p>\n              </div>\n            )}\n          </div>\n\n          {/* Text File Upload */}\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center\">\n              <FileText className=\"h-5 w-5 mr-2\" />\n              Upload Text File\n            </h3>\n            <div\n              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${\n                dragOver === 'text'\n                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'\n                  : 'border-gray-300 dark:border-gray-600 hover:border-blue-400'\n              }`}\n              onDragOver={(e) => handleDragOver(e, 'text')}\n              onDragLeave={handleDragLeave}\n              onDrop={(e) => handleDrop(e, 'text')}\n            >\n              <FileText className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-600 dark:text-gray-400 mb-2\">\n                Upload a text file with words to remove\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-500\">\n                One word or phrase per line (max 1MB)\n              </p>\n              <input\n                type=\"file\"\n                accept=\".txt,.text\"\n                className=\"hidden\"\n                id=\"text-upload\"\n                onChange={(e) => {\n                  const file = e.target.files?.[0];\n                  if (file) {\n                    handleTextFile(file);\n                  }\n                }}\n              />\n              <label\n                htmlFor=\"text-upload\"\n                className=\"mt-4 inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors\"\n              >\n                Choose Text File\n              </label>\n            </div>\n            {textFile && (\n              <div className=\"mt-4 space-y-2\">\n                <div className=\"p-3 bg-green-50 dark:bg-green-900/20 rounded-md\">\n                  <p className=\"text-sm text-green-800 dark:text-green-400\">\n                    ✓ {textFile.name} ({(textFile.size / 1024).toFixed(2)} KB)\n                  </p>\n                </div>\n                {textFileContent.length > 0 && (\n                  <div className=\"p-3 bg-gray-50 dark:bg-gray-700 rounded-md\">\n                    <p className=\"text-xs text-gray-600 dark:text-gray-400 mb-2\">\n                      Text to remove ({textFileContent.length} entries):\n                    </p>\n                    <div className=\"text-xs text-gray-800 dark:text-gray-200 max-h-20 overflow-y-auto\">\n                      {textFileContent.slice(0, 10).map((text, index) => (\n                        <div key={index} className=\"truncate\">• {text}</div>\n                      ))}\n                      {textFileContent.length > 10 && (\n                        <div className=\"text-gray-500 italic\">... and {textFileContent.length - 10} more</div>\n                      )}\n                    </div>\n                  </div>\n                )}\n              </div>\n            )}\n          </div>\n        </div>\n\n        {/* Error Display */}\n        {error && (\n          <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-8\">\n            <p className=\"text-red-800 dark:text-red-400 text-sm\">\n              ⚠️ {error}\n            </p>\n          </div>\n        )}\n\n        {/* Success Display */}\n        {successMessage && (\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-8\">\n            <p className=\"text-green-800 dark:text-green-400 text-sm\">\n              ✅ {successMessage}\n            </p>\n          </div>\n        )}\n\n        {/* Process Button */}\n        <div className=\"text-center mb-8\">\n          <button\n            disabled={!imageFile || !textFile || isProcessing}\n            onClick={processImage}\n            className=\"inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\"\n          >\n            {isProcessing ? (\n              <>\n                <Loader2 className=\"h-5 w-5 mr-2 animate-spin\" />\n                {processingStatus || 'Processing...'}\n              </>\n            ) : (\n              'Process Image'\n            )}\n          </button>\n        </div>\n\n        {/* Results Section */}\n        {(originalImageUrl || processedImageUrl) && (\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6\">\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-6\">\n              Results\n            </h3>\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {/* Original Image */}\n              {originalImageUrl && (\n                <div>\n                  <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300 mb-3\">\n                    Original Image\n                  </h4>\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden\">\n                    <img\n                      src={originalImageUrl}\n                      alt=\"Original\"\n                      className=\"w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700\"\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Processed Image */}\n              {processedImageUrl && (\n                <div>\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <h4 className=\"text-md font-medium text-gray-700 dark:text-gray-300\">\n                      Processed Image\n                    </h4>\n                    <button\n                      onClick={downloadProcessedImage}\n                      className=\"inline-flex items-center px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 transition-colors\"\n                    >\n                      <Download className=\"h-4 w-4 mr-1\" />\n                      Download\n                    </button>\n                  </div>\n                  <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden\">\n                    <img\n                      src={processedImageUrl}\n                      alt=\"Processed\"\n                      className=\"w-full h-auto max-h-96 object-contain bg-gray-50 dark:bg-gray-700\"\n                    />\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B;IAClE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAChE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnE,kBAAkB;IAClB,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa;YAAC;YAAc;YAAa;YAAa;YAAa;YAAc;SAAY;QACnG,MAAM,UAAU,KAAK,OAAO,MAAM,OAAO;QAEzC,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,GAAG;YACnC,SAAS;YACT,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,aAAa;YAAC;SAAa;QACjC,MAAM,UAAU,OAAO,MAAM,MAAM;QAEnC,IAAI,CAAC,WAAW,QAAQ,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,SAAS;YAClE,SAAS;YACT,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,SAAS;YACT,OAAO;QACT;QAEA,OAAO;IACT;IAEA,8BAA8B;IAC9B,MAAM,kBAAkB,CAAC;QACvB,SAAS;QACT,kBAAkB;QAClB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QAEjB,IAAI,kBAAkB,OAAO;YAC3B,aAAa;YACb,oBAAoB,IAAI,eAAe,CAAC;QAC1C;IACF;IAEA,6BAA6B;IAC7B,MAAM,iBAAiB,OAAO;QAC5B,SAAS;QACT,kBAAkB;QAClB,IAAI,iBAAiB,OAAO;YAC1B,YAAY;YACZ,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;gBACnC,mBAAmB;gBACnB,kBAAkB,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,uBAAuB,CAAC;YACrE,EAAE,OAAO,OAAO;gBACd,SAAS;YACX;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,iBAAiB,CAAC,GAAoB;QAC1C,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,YAAY;IACd;IAEA,MAAM,aAAa,CAAC,GAAoB;QACtC,EAAE,cAAc;QAChB,YAAY;QAEZ,MAAM,QAAQ,MAAM,IAAI,CAAC,EAAE,YAAY,CAAC,KAAK;QAC7C,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,SAAS,SAAS;gBACpB,gBAAgB;YAClB,OAAO;gBACL,eAAe;YACjB;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe;QACnB,IAAI,CAAC,aAAa,CAAC,UAAU;YAC3B,SAAS;YACT;QACF;QAEA,gBAAgB;QAChB,SAAS;QACT,oBAAoB;QAEpB,IAAI;YACF,yBAAyB;YACzB,MAAM,sHAAA,CAAA,aAAU,CAAC,UAAU;YAE3B,yBAAyB;YACzB,oBAAoB;YACpB,MAAM,cAAc,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;YAEvC,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,MAAM,IAAI,MAAM;YAClB;YAEA,2BAA2B;YAC3B,oBAAoB;YACpB,MAAM,YAAY,MAAM,sHAAA,CAAA,aAAU,CAAC,aAAa,CAAC;YAEjD,qBAAqB;YACrB,oBAAoB;YACpB,MAAM,cAAc,sHAAA,CAAA,aAAU,CAAC,eAAe,CAAC,WAAW;YAE1D,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,UAAU,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,MAAM,CAAC,QAAQ,CAAC;YAE/F,IAAI,YAAY,MAAM,KAAK,GAAG;gBAC5B,SAAS,CAAC,oDAAoD,EAAE,UAAU,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO,UAAU,IAAI,CAAC,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC,CAAC;gBAC9I;YACF;YAEA,+BAA+B;YAC/B,oBAAoB;YACpB,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;YACvC,MAAM,gBAAgB,MAAM,eAAe,mBAAmB,CAC5D,WACA,aACA;gBAAE,WAAW;gBAAW,SAAS;YAAE;YAGrC,iCAAiC;YACjC,MAAM,eAAe,IAAI,eAAe,CAAC;YACzC,qBAAqB;YACrB,iBAAiB;YAEjB,kBAAkB,CAAC,qBAAqB,EAAE,YAAY,MAAM,CAAC,WAAW,EAAE,YAAY,MAAM,GAAG,IAAI,OAAO,GAAG,gBAAgB,CAAC;YAC9H,oBAAoB;QAEtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,SAAU;YACR,gBAAgB;YAChB,oBAAoB;QACtB;IACF;IAEA,2BAA2B;IAC3B,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB,WAAW;YAC9B,MAAM,WAAW,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC;YAC3E,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,eAAe;QACxB;IACF;IAEA,mBAAmB;IACnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;kCAAO;oBACL,8CAA8C;oBAC9C,IAAI,kBAAkB;wBACpB,IAAI,eAAe,CAAC;oBACtB;oBACA,IAAI,mBAAmB;wBACrB,IAAI,eAAe,CAAC;oBACtB;oBACA,uBAAuB;oBACvB,sHAAA,CAAA,aAAU,CAAC,SAAS;gBACtB;;QACF;yBAAG;QAAC;QAAkB;KAAkB;IAExC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uMAAA,CAAA,QAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAmD;;;;;;sDAGjE,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUlE,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;kCAKR,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,uMAAA,CAAA,QAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,6LAAC;wCACC,WAAW,CAAC,oEAAoE,EAC9E,aAAa,UACT,mDACA,8DACJ;wCACF,YAAY,CAAC,IAAM,eAAe,GAAG;wCACrC,aAAa;wCACb,QAAQ,CAAC,IAAM,WAAW,GAAG;;0DAE7B,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;0DAGrD,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,6LAAC;gDACC,MAAK;gDACL,QAAO;gDACP,WAAU;gDACV,IAAG;gDACH,UAAU,CAAC;oDACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oDAChC,IAAI,MAAM;wDACR,gBAAgB;oDAClB;gDACF;;;;;;0DAEF,6LAAC;gDACC,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;;oCAIF,2BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;gDAA6C;gDACrD,UAAU,IAAI;gDAAC;gDAAG,CAAC,UAAU,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;0CAOvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,6LAAC;wCACC,WAAW,CAAC,oEAAoE,EAC9E,aAAa,SACT,mDACA,8DACJ;wCACF,YAAY,CAAC,IAAM,eAAe,GAAG;wCACrC,aAAa;wCACb,QAAQ,CAAC,IAAM,WAAW,GAAG;;0DAE7B,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;0DAGrD,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;0DAGxD,6LAAC;gDACC,MAAK;gDACL,QAAO;gDACP,WAAU;gDACV,IAAG;gDACH,UAAU,CAAC;oDACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oDAChC,IAAI,MAAM;wDACR,eAAe;oDACjB;gDACF;;;;;;0DAEF,6LAAC;gDACC,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;;oCAIF,0BACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;wDAA6C;wDACrD,SAAS,IAAI;wDAAC;wDAAG,CAAC,SAAS,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;4CAGzD,gBAAgB,MAAM,GAAG,mBACxB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAgD;4DAC1C,gBAAgB,MAAM;4DAAC;;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;;4DACZ,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,MAAM,sBACvC,6LAAC;oEAAgB,WAAU;;wEAAW;wEAAG;;mEAA/B;;;;;4DAEX,gBAAgB,MAAM,GAAG,oBACxB,6LAAC;gEAAI,WAAU;;oEAAuB;oEAAS,gBAAgB,MAAM,GAAG;oEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAW1F,uBACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAyC;gCAChD;;;;;;;;;;;;oBAMT,gCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAA6C;gCACrD;;;;;;;;;;;;kCAMT,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,UAAU,CAAC,aAAa,CAAC,YAAY;4BACrC,SAAS;4BACT,WAAU;sCAET,6BACC;;kDACE,6LAAC,oNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAClB,oBAAoB;;+CAGvB;;;;;;;;;;;oBAML,CAAC,oBAAoB,iBAAiB,mBACrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyD;;;;;;0CAGvE,6LAAC;gCAAI,WAAU;;oCAEZ,kCACC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;oCAOjB,mCACC,6LAAC;;0DACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAuD;;;;;;kEAGrE,6LAAC;wDACC,SAAS;wDACT,WAAU;;0EAEV,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;0DAIzC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK;oDACL,KAAI;oDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWhC;GAxbwB;KAAA", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "file": "utils.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,CAAA,CAA2C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "file": "defaultAttributes.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1397, "column": 0}, "map": {"version": 3, "file": "Icon.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,CACE,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAEL,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uKAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1437, "column": 0}, "map": {"version": 3, "file": "createLucideIcon.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,CAAC,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iKACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,CAAA,CAAA,EAC7C,CAAA,OAAA,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,EAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "file": "upload.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/icons/upload.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3v12', key: '1x0j5s' }],\n  ['path', { d: 'm17 8-5-5-5 5', key: '7q97r8' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n];\n\n/**\n * @component @name Upload\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM3YxMiIgLz4KICA8cGF0aCBkPSJtMTcgOC01LTUtNSA1IiAvPgogIDxwYXRoIGQ9Ik0yMSAxNXY0YTIgMiAwIDAgMS0yIDJINWEyIDIgMCAwIDEtMi0ydi00IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/upload\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Upload = createLucideIcon('upload', __iconNode);\n\nexport default Upload;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1522, "column": 0}, "map": {"version": 3, "file": "file-text.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/icons/file-text.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z', key: '1rqfz7' }],\n  ['path', { d: 'M14 2v4a2 2 0 0 0 2 2h4', key: 'tnqrlb' }],\n  ['path', { d: 'M10 9H8', key: 'b1mrlr' }],\n  ['path', { d: 'M16 13H8', key: 't4e002' }],\n  ['path', { d: 'M16 17H8', key: 'z1uh3a' }],\n];\n\n/**\n * @component @name FileText\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTUgMkg2YTIgMiAwIDAgMC0yIDJ2MTZhMiAyIDAgMCAwIDIgMmgxMmEyIDIgMCAwIDAgMi0yVjdaIiAvPgogIDxwYXRoIGQ9Ik0xNCAydjRhMiAyIDAgMCAwIDIgMmg0IiAvPgogIDxwYXRoIGQ9Ik0xMCA5SDgiIC8+CiAgPHBhdGggZD0iTTE2IDEzSDgiIC8+CiAgPHBhdGggZD0iTTE2IDE3SDgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/file-text\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FileText = createLucideIcon('file-text', __iconNode);\n\nexport default FileText;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAa,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1589, "column": 0}, "map": {"version": 3, "file": "image.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/icons/image.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '18', height: '18', x: '3', y: '3', rx: '2', ry: '2', key: '1m3agn' }],\n  ['circle', { cx: '9', cy: '9', r: '2', key: 'af1f0g' }],\n  ['path', { d: 'm21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21', key: '1xmnt7' }],\n];\n\n/**\n * @component @name Image\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHg9IjMiIHk9IjMiIHJ4PSIyIiByeT0iMiIgLz4KICA8Y2lyY2xlIGN4PSI5IiBjeT0iOSIgcj0iMiIgLz4KICA8cGF0aCBkPSJtMjEgMTUtMy4wODYtMy4wODZhMiAyIDAgMCAwLTIuODI4IDBMNiAyMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/image\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Image = createLucideIcon('image', __iconNode);\n\nexport default Image;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,KAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,EAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAG;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvF;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACtD;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA6C,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1649, "column": 0}, "map": {"version": 3, "file": "download.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/icons/download.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 15V3', key: 'm9g1x1' }],\n  ['path', { d: 'M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4', key: 'ih7n3h' }],\n  ['path', { d: 'm7 10 5 5 5-5', key: 'brsn70' }],\n];\n\n/**\n * @component @name Download\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMTVWMyIgLz4KICA8cGF0aCBkPSJNMjEgMTV2NGEyIDIgMCAwIDEtMiAySDVhMiAyIDAgMCAxLTItMnYtNCIgLz4KICA8cGF0aCBkPSJtNyAxMCA1IDUgNS01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/download\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Download = createLucideIcon('download', __iconNode);\n\nexport default Download;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAChD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1702, "column": 0}, "map": {"version": 3, "file": "loader-circle.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,6BAA+B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa5F,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1740, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/regenerator-runtime/runtime.js"], "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nvar runtime = (function (exports) {\n  \"use strict\";\n\n  var Op = Object.prototype;\n  var hasOwn = Op.hasOwnProperty;\n  var defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; };\n  var undefined; // More compressible than void 0.\n  var $Symbol = typeof Symbol === \"function\" ? Symbol : {};\n  var iteratorSymbol = $Symbol.iterator || \"@@iterator\";\n  var asyncIteratorSymbol = $Symbol.asyncIterator || \"@@asyncIterator\";\n  var toStringTagSymbol = $Symbol.toStringTag || \"@@toStringTag\";\n\n  function define(obj, key, value) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n    return obj[key];\n  }\n  try {\n    // IE 8 has a broken Object.defineProperty that only works on DOM objects.\n    define({}, \"\");\n  } catch (err) {\n    define = function(obj, key, value) {\n      return obj[key] = value;\n    };\n  }\n\n  function wrap(innerFn, outerFn, self, tryLocsList) {\n    // If outerFn provided and outerFn.prototype is a Generator, then outerFn.prototype instanceof Generator.\n    var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator;\n    var generator = Object.create(protoGenerator.prototype);\n    var context = new Context(tryLocsList || []);\n\n    // The ._invoke method unifies the implementations of the .next,\n    // .throw, and .return methods.\n    defineProperty(generator, \"_invoke\", { value: makeInvokeMethod(innerFn, self, context) });\n\n    return generator;\n  }\n  exports.wrap = wrap;\n\n  // Try/catch helper to minimize deoptimizations. Returns a completion\n  // record like context.tryEntries[i].completion. This interface could\n  // have been (and was previously) designed to take a closure to be\n  // invoked without arguments, but in all the cases we care about we\n  // already have an existing method we want to call, so there's no need\n  // to create a new function object. We can even get away with assuming\n  // the method takes exactly one argument, since that happens to be true\n  // in every case, so we don't have to touch the arguments object. The\n  // only additional allocation required is the completion record, which\n  // has a stable shape and so hopefully should be cheap to allocate.\n  function tryCatch(fn, obj, arg) {\n    try {\n      return { type: \"normal\", arg: fn.call(obj, arg) };\n    } catch (err) {\n      return { type: \"throw\", arg: err };\n    }\n  }\n\n  var GenStateSuspendedStart = \"suspendedStart\";\n  var GenStateSuspendedYield = \"suspendedYield\";\n  var GenStateExecuting = \"executing\";\n  var GenStateCompleted = \"completed\";\n\n  // Returning this object from the innerFn has the same effect as\n  // breaking out of the dispatch switch statement.\n  var ContinueSentinel = {};\n\n  // Dummy constructor functions that we use as the .constructor and\n  // .constructor.prototype properties for functions that return Generator\n  // objects. For full spec compliance, you may wish to configure your\n  // minifier not to mangle the names of these two functions.\n  function Generator() {}\n  function GeneratorFunction() {}\n  function GeneratorFunctionPrototype() {}\n\n  // This is a polyfill for %IteratorPrototype% for environments that\n  // don't natively support it.\n  var IteratorPrototype = {};\n  define(IteratorPrototype, iteratorSymbol, function () {\n    return this;\n  });\n\n  var getProto = Object.getPrototypeOf;\n  var NativeIteratorPrototype = getProto && getProto(getProto(values([])));\n  if (NativeIteratorPrototype &&\n      NativeIteratorPrototype !== Op &&\n      hasOwn.call(NativeIteratorPrototype, iteratorSymbol)) {\n    // This environment has a native %IteratorPrototype%; use it instead\n    // of the polyfill.\n    IteratorPrototype = NativeIteratorPrototype;\n  }\n\n  var Gp = GeneratorFunctionPrototype.prototype =\n    Generator.prototype = Object.create(IteratorPrototype);\n  GeneratorFunction.prototype = GeneratorFunctionPrototype;\n  defineProperty(Gp, \"constructor\", { value: GeneratorFunctionPrototype, configurable: true });\n  defineProperty(\n    GeneratorFunctionPrototype,\n    \"constructor\",\n    { value: GeneratorFunction, configurable: true }\n  );\n  GeneratorFunction.displayName = define(\n    GeneratorFunctionPrototype,\n    toStringTagSymbol,\n    \"GeneratorFunction\"\n  );\n\n  // Helper for defining the .next, .throw, and .return methods of the\n  // Iterator interface in terms of a single ._invoke method.\n  function defineIteratorMethods(prototype) {\n    [\"next\", \"throw\", \"return\"].forEach(function(method) {\n      define(prototype, method, function(arg) {\n        return this._invoke(method, arg);\n      });\n    });\n  }\n\n  exports.isGeneratorFunction = function(genFun) {\n    var ctor = typeof genFun === \"function\" && genFun.constructor;\n    return ctor\n      ? ctor === GeneratorFunction ||\n        // For the native GeneratorFunction constructor, the best we can\n        // do is to check its .name property.\n        (ctor.displayName || ctor.name) === \"GeneratorFunction\"\n      : false;\n  };\n\n  exports.mark = function(genFun) {\n    if (Object.setPrototypeOf) {\n      Object.setPrototypeOf(genFun, GeneratorFunctionPrototype);\n    } else {\n      genFun.__proto__ = GeneratorFunctionPrototype;\n      define(genFun, toStringTagSymbol, \"GeneratorFunction\");\n    }\n    genFun.prototype = Object.create(Gp);\n    return genFun;\n  };\n\n  // Within the body of any async function, `await x` is transformed to\n  // `yield regeneratorRuntime.awrap(x)`, so that the runtime can test\n  // `hasOwn.call(value, \"__await\")` to determine if the yielded value is\n  // meant to be awaited.\n  exports.awrap = function(arg) {\n    return { __await: arg };\n  };\n\n  function AsyncIterator(generator, PromiseImpl) {\n    function invoke(method, arg, resolve, reject) {\n      var record = tryCatch(generator[method], generator, arg);\n      if (record.type === \"throw\") {\n        reject(record.arg);\n      } else {\n        var result = record.arg;\n        var value = result.value;\n        if (value &&\n            typeof value === \"object\" &&\n            hasOwn.call(value, \"__await\")) {\n          return PromiseImpl.resolve(value.__await).then(function(value) {\n            invoke(\"next\", value, resolve, reject);\n          }, function(err) {\n            invoke(\"throw\", err, resolve, reject);\n          });\n        }\n\n        return PromiseImpl.resolve(value).then(function(unwrapped) {\n          // When a yielded Promise is resolved, its final value becomes\n          // the .value of the Promise<{value,done}> result for the\n          // current iteration.\n          result.value = unwrapped;\n          resolve(result);\n        }, function(error) {\n          // If a rejected Promise was yielded, throw the rejection back\n          // into the async generator function so it can be handled there.\n          return invoke(\"throw\", error, resolve, reject);\n        });\n      }\n    }\n\n    var previousPromise;\n\n    function enqueue(method, arg) {\n      function callInvokeWithMethodAndArg() {\n        return new PromiseImpl(function(resolve, reject) {\n          invoke(method, arg, resolve, reject);\n        });\n      }\n\n      return previousPromise =\n        // If enqueue has been called before, then we want to wait until\n        // all previous Promises have been resolved before calling invoke,\n        // so that results are always delivered in the correct order. If\n        // enqueue has not been called before, then it is important to\n        // call invoke immediately, without waiting on a callback to fire,\n        // so that the async generator function has the opportunity to do\n        // any necessary setup in a predictable way. This predictability\n        // is why the Promise constructor synchronously invokes its\n        // executor callback, and why async functions synchronously\n        // execute code before the first await. Since we implement simple\n        // async functions in terms of async generators, it is especially\n        // important to get this right, even though it requires care.\n        previousPromise ? previousPromise.then(\n          callInvokeWithMethodAndArg,\n          // Avoid propagating failures to Promises returned by later\n          // invocations of the iterator.\n          callInvokeWithMethodAndArg\n        ) : callInvokeWithMethodAndArg();\n    }\n\n    // Define the unified helper method that is used to implement .next,\n    // .throw, and .return (see defineIteratorMethods).\n    defineProperty(this, \"_invoke\", { value: enqueue });\n  }\n\n  defineIteratorMethods(AsyncIterator.prototype);\n  define(AsyncIterator.prototype, asyncIteratorSymbol, function () {\n    return this;\n  });\n  exports.AsyncIterator = AsyncIterator;\n\n  // Note that simple async functions are implemented on top of\n  // AsyncIterator objects; they just return a Promise for the value of\n  // the final result produced by the iterator.\n  exports.async = function(innerFn, outerFn, self, tryLocsList, PromiseImpl) {\n    if (PromiseImpl === void 0) PromiseImpl = Promise;\n\n    var iter = new AsyncIterator(\n      wrap(innerFn, outerFn, self, tryLocsList),\n      PromiseImpl\n    );\n\n    return exports.isGeneratorFunction(outerFn)\n      ? iter // If outerFn is a generator, return the full iterator.\n      : iter.next().then(function(result) {\n          return result.done ? result.value : iter.next();\n        });\n  };\n\n  function makeInvokeMethod(innerFn, self, context) {\n    var state = GenStateSuspendedStart;\n\n    return function invoke(method, arg) {\n      if (state === GenStateExecuting) {\n        throw new Error(\"Generator is already running\");\n      }\n\n      if (state === GenStateCompleted) {\n        if (method === \"throw\") {\n          throw arg;\n        }\n\n        // Be forgiving, per 25.3.3.3.3 of the spec:\n        // https://people.mozilla.org/~jorendorff/es6-draft.html#sec-generatorresume\n        return doneResult();\n      }\n\n      context.method = method;\n      context.arg = arg;\n\n      while (true) {\n        var delegate = context.delegate;\n        if (delegate) {\n          var delegateResult = maybeInvokeDelegate(delegate, context);\n          if (delegateResult) {\n            if (delegateResult === ContinueSentinel) continue;\n            return delegateResult;\n          }\n        }\n\n        if (context.method === \"next\") {\n          // Setting context._sent for legacy support of Babel's\n          // function.sent implementation.\n          context.sent = context._sent = context.arg;\n\n        } else if (context.method === \"throw\") {\n          if (state === GenStateSuspendedStart) {\n            state = GenStateCompleted;\n            throw context.arg;\n          }\n\n          context.dispatchException(context.arg);\n\n        } else if (context.method === \"return\") {\n          context.abrupt(\"return\", context.arg);\n        }\n\n        state = GenStateExecuting;\n\n        var record = tryCatch(innerFn, self, context);\n        if (record.type === \"normal\") {\n          // If an exception is thrown from innerFn, we leave state ===\n          // GenStateExecuting and loop back for another invocation.\n          state = context.done\n            ? GenStateCompleted\n            : GenStateSuspendedYield;\n\n          if (record.arg === ContinueSentinel) {\n            continue;\n          }\n\n          return {\n            value: record.arg,\n            done: context.done\n          };\n\n        } else if (record.type === \"throw\") {\n          state = GenStateCompleted;\n          // Dispatch the exception by looping back around to the\n          // context.dispatchException(context.arg) call above.\n          context.method = \"throw\";\n          context.arg = record.arg;\n        }\n      }\n    };\n  }\n\n  // Call delegate.iterator[context.method](context.arg) and handle the\n  // result, either by returning a { value, done } result from the\n  // delegate iterator, or by modifying context.method and context.arg,\n  // setting context.delegate to null, and returning the ContinueSentinel.\n  function maybeInvokeDelegate(delegate, context) {\n    var methodName = context.method;\n    var method = delegate.iterator[methodName];\n    if (method === undefined) {\n      // A .throw or .return when the delegate iterator has no .throw\n      // method, or a missing .next mehtod, always terminate the\n      // yield* loop.\n      context.delegate = null;\n\n      // Note: [\"return\"] must be used for ES3 parsing compatibility.\n      if (methodName === \"throw\" && delegate.iterator[\"return\"]) {\n        // If the delegate iterator has a return method, give it a\n        // chance to clean up.\n        context.method = \"return\";\n        context.arg = undefined;\n        maybeInvokeDelegate(delegate, context);\n\n        if (context.method === \"throw\") {\n          // If maybeInvokeDelegate(context) changed context.method from\n          // \"return\" to \"throw\", let that override the TypeError below.\n          return ContinueSentinel;\n        }\n      }\n      if (methodName !== \"return\") {\n        context.method = \"throw\";\n        context.arg = new TypeError(\n          \"The iterator does not provide a '\" + methodName + \"' method\");\n      }\n\n      return ContinueSentinel;\n    }\n\n    var record = tryCatch(method, delegate.iterator, context.arg);\n\n    if (record.type === \"throw\") {\n      context.method = \"throw\";\n      context.arg = record.arg;\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    var info = record.arg;\n\n    if (! info) {\n      context.method = \"throw\";\n      context.arg = new TypeError(\"iterator result is not an object\");\n      context.delegate = null;\n      return ContinueSentinel;\n    }\n\n    if (info.done) {\n      // Assign the result of the finished delegate to the temporary\n      // variable specified by delegate.resultName (see delegateYield).\n      context[delegate.resultName] = info.value;\n\n      // Resume execution at the desired location (see delegateYield).\n      context.next = delegate.nextLoc;\n\n      // If context.method was \"throw\" but the delegate handled the\n      // exception, let the outer generator proceed normally. If\n      // context.method was \"next\", forget context.arg since it has been\n      // \"consumed\" by the delegate iterator. If context.method was\n      // \"return\", allow the original .return call to continue in the\n      // outer generator.\n      if (context.method !== \"return\") {\n        context.method = \"next\";\n        context.arg = undefined;\n      }\n\n    } else {\n      // Re-yield the result returned by the delegate method.\n      return info;\n    }\n\n    // The delegate iterator is finished, so forget it and continue with\n    // the outer generator.\n    context.delegate = null;\n    return ContinueSentinel;\n  }\n\n  // Define Generator.prototype.{next,throw,return} in terms of the\n  // unified ._invoke helper method.\n  defineIteratorMethods(Gp);\n\n  define(Gp, toStringTagSymbol, \"Generator\");\n\n  // A Generator should always return itself as the iterator object when the\n  // @@iterator function is called on it. Some browsers' implementations of the\n  // iterator prototype chain incorrectly implement this, causing the Generator\n  // object to not be returned from this call. This ensures that doesn't happen.\n  // See https://github.com/facebook/regenerator/issues/274 for more details.\n  define(Gp, iteratorSymbol, function() {\n    return this;\n  });\n\n  define(Gp, \"toString\", function() {\n    return \"[object Generator]\";\n  });\n\n  function pushTryEntry(locs) {\n    var entry = { tryLoc: locs[0] };\n\n    if (1 in locs) {\n      entry.catchLoc = locs[1];\n    }\n\n    if (2 in locs) {\n      entry.finallyLoc = locs[2];\n      entry.afterLoc = locs[3];\n    }\n\n    this.tryEntries.push(entry);\n  }\n\n  function resetTryEntry(entry) {\n    var record = entry.completion || {};\n    record.type = \"normal\";\n    delete record.arg;\n    entry.completion = record;\n  }\n\n  function Context(tryLocsList) {\n    // The root entry object (effectively a try statement without a catch\n    // or a finally block) gives us a place to store values thrown from\n    // locations where there is no enclosing try statement.\n    this.tryEntries = [{ tryLoc: \"root\" }];\n    tryLocsList.forEach(pushTryEntry, this);\n    this.reset(true);\n  }\n\n  exports.keys = function(val) {\n    var object = Object(val);\n    var keys = [];\n    for (var key in object) {\n      keys.push(key);\n    }\n    keys.reverse();\n\n    // Rather than returning an object with a next method, we keep\n    // things simple and return the next function itself.\n    return function next() {\n      while (keys.length) {\n        var key = keys.pop();\n        if (key in object) {\n          next.value = key;\n          next.done = false;\n          return next;\n        }\n      }\n\n      // To avoid creating an additional object, we just hang the .value\n      // and .done properties off the next function object itself. This\n      // also ensures that the minifier will not anonymize the function.\n      next.done = true;\n      return next;\n    };\n  };\n\n  function values(iterable) {\n    if (iterable) {\n      var iteratorMethod = iterable[iteratorSymbol];\n      if (iteratorMethod) {\n        return iteratorMethod.call(iterable);\n      }\n\n      if (typeof iterable.next === \"function\") {\n        return iterable;\n      }\n\n      if (!isNaN(iterable.length)) {\n        var i = -1, next = function next() {\n          while (++i < iterable.length) {\n            if (hasOwn.call(iterable, i)) {\n              next.value = iterable[i];\n              next.done = false;\n              return next;\n            }\n          }\n\n          next.value = undefined;\n          next.done = true;\n\n          return next;\n        };\n\n        return next.next = next;\n      }\n    }\n\n    // Return an iterator with no values.\n    return { next: doneResult };\n  }\n  exports.values = values;\n\n  function doneResult() {\n    return { value: undefined, done: true };\n  }\n\n  Context.prototype = {\n    constructor: Context,\n\n    reset: function(skipTempReset) {\n      this.prev = 0;\n      this.next = 0;\n      // Resetting context._sent for legacy support of Babel's\n      // function.sent implementation.\n      this.sent = this._sent = undefined;\n      this.done = false;\n      this.delegate = null;\n\n      this.method = \"next\";\n      this.arg = undefined;\n\n      this.tryEntries.forEach(resetTryEntry);\n\n      if (!skipTempReset) {\n        for (var name in this) {\n          // Not sure about the optimal order of these conditions:\n          if (name.charAt(0) === \"t\" &&\n              hasOwn.call(this, name) &&\n              !isNaN(+name.slice(1))) {\n            this[name] = undefined;\n          }\n        }\n      }\n    },\n\n    stop: function() {\n      this.done = true;\n\n      var rootEntry = this.tryEntries[0];\n      var rootRecord = rootEntry.completion;\n      if (rootRecord.type === \"throw\") {\n        throw rootRecord.arg;\n      }\n\n      return this.rval;\n    },\n\n    dispatchException: function(exception) {\n      if (this.done) {\n        throw exception;\n      }\n\n      var context = this;\n      function handle(loc, caught) {\n        record.type = \"throw\";\n        record.arg = exception;\n        context.next = loc;\n\n        if (caught) {\n          // If the dispatched exception was caught by a catch block,\n          // then let that catch block handle the exception normally.\n          context.method = \"next\";\n          context.arg = undefined;\n        }\n\n        return !! caught;\n      }\n\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        var record = entry.completion;\n\n        if (entry.tryLoc === \"root\") {\n          // Exception thrown outside of any try block that could handle\n          // it, so set the completion value of the entire function to\n          // throw the exception.\n          return handle(\"end\");\n        }\n\n        if (entry.tryLoc <= this.prev) {\n          var hasCatch = hasOwn.call(entry, \"catchLoc\");\n          var hasFinally = hasOwn.call(entry, \"finallyLoc\");\n\n          if (hasCatch && hasFinally) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            } else if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else if (hasCatch) {\n            if (this.prev < entry.catchLoc) {\n              return handle(entry.catchLoc, true);\n            }\n\n          } else if (hasFinally) {\n            if (this.prev < entry.finallyLoc) {\n              return handle(entry.finallyLoc);\n            }\n\n          } else {\n            throw new Error(\"try statement without catch or finally\");\n          }\n        }\n      }\n    },\n\n    abrupt: function(type, arg) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc <= this.prev &&\n            hasOwn.call(entry, \"finallyLoc\") &&\n            this.prev < entry.finallyLoc) {\n          var finallyEntry = entry;\n          break;\n        }\n      }\n\n      if (finallyEntry &&\n          (type === \"break\" ||\n           type === \"continue\") &&\n          finallyEntry.tryLoc <= arg &&\n          arg <= finallyEntry.finallyLoc) {\n        // Ignore the finally entry if control is not jumping to a\n        // location outside the try/catch block.\n        finallyEntry = null;\n      }\n\n      var record = finallyEntry ? finallyEntry.completion : {};\n      record.type = type;\n      record.arg = arg;\n\n      if (finallyEntry) {\n        this.method = \"next\";\n        this.next = finallyEntry.finallyLoc;\n        return ContinueSentinel;\n      }\n\n      return this.complete(record);\n    },\n\n    complete: function(record, afterLoc) {\n      if (record.type === \"throw\") {\n        throw record.arg;\n      }\n\n      if (record.type === \"break\" ||\n          record.type === \"continue\") {\n        this.next = record.arg;\n      } else if (record.type === \"return\") {\n        this.rval = this.arg = record.arg;\n        this.method = \"return\";\n        this.next = \"end\";\n      } else if (record.type === \"normal\" && afterLoc) {\n        this.next = afterLoc;\n      }\n\n      return ContinueSentinel;\n    },\n\n    finish: function(finallyLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.finallyLoc === finallyLoc) {\n          this.complete(entry.completion, entry.afterLoc);\n          resetTryEntry(entry);\n          return ContinueSentinel;\n        }\n      }\n    },\n\n    \"catch\": function(tryLoc) {\n      for (var i = this.tryEntries.length - 1; i >= 0; --i) {\n        var entry = this.tryEntries[i];\n        if (entry.tryLoc === tryLoc) {\n          var record = entry.completion;\n          if (record.type === \"throw\") {\n            var thrown = record.arg;\n            resetTryEntry(entry);\n          }\n          return thrown;\n        }\n      }\n\n      // The context.catch method must only be called with a location\n      // argument that corresponds to a known catch block.\n      throw new Error(\"illegal catch attempt\");\n    },\n\n    delegateYield: function(iterable, resultName, nextLoc) {\n      this.delegate = {\n        iterator: values(iterable),\n        resultName: resultName,\n        nextLoc: nextLoc\n      };\n\n      if (this.method === \"next\") {\n        // Deliberately forget the last sent value so that we don't\n        // accidentally pass it on to the delegate.\n        this.arg = undefined;\n      }\n\n      return ContinueSentinel;\n    }\n  };\n\n  // Regardless of whether this script is executing as a CommonJS module\n  // or not, return the runtime object so that we can declare the variable\n  // regeneratorRuntime in the outer scope, which allows this module to be\n  // injected easily by `bin/regenerator --include-runtime script.js`.\n  return exports;\n\n}(\n  // If this script is executing as a CommonJS module, use module.exports\n  // as the regeneratorRuntime namespace. Otherwise create a new empty\n  // object. Either way, the resulting object will be used to initialize\n  // the regeneratorRuntime variable at the top of this file.\n  typeof module === \"object\" ? module.exports : {}\n));\n\ntry {\n  regeneratorRuntime = runtime;\n} catch (accidentalStrictMode) {\n  // This module should not be running in strict mode, so the above\n  // assignment should always work unless something is misconfigured. Just\n  // in case runtime.js accidentally runs in strict mode, in modern engines\n  // we can explicitly access globalThis. In older engines we can escape\n  // strict mode using a global Function call. This could conceivably fail\n  // if a Content Security Policy forbids using Function, but in that case\n  // the proper solution is to fix the accidental strict mode problem. If\n  // you've misconfigured your bundler to force strict mode and applied a\n  // CSP to forbid Function, and you're not willing to fix either of those\n  // problems, please detail your unique predicament in a GitHub issue.\n  if (typeof globalThis === \"object\") {\n    globalThis.regeneratorRuntime = runtime;\n  } else {\n    Function(\"r\", \"regeneratorRuntime = r\")(runtime);\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GAED,IAAI,UAAW,SAAU,OAAO;IAC9B;IAEA,IAAI,KAAK,OAAO,SAAS;IACzB,IAAI,SAAS,GAAG,cAAc;IAC9B,IAAI,iBAAiB,OAAO,cAAc,IAAI,SAAU,GAAG,EAAE,GAAG,EAAE,IAAI;QAAI,GAAG,CAAC,IAAI,GAAG,KAAK,KAAK;IAAE;IACjG,IAAI,WAAW,iCAAiC;IAChD,IAAI,UAAU,OAAO,WAAW,aAAa,SAAS,CAAC;IACvD,IAAI,iBAAiB,QAAQ,QAAQ,IAAI;IACzC,IAAI,sBAAsB,QAAQ,aAAa,IAAI;IACnD,IAAI,oBAAoB,QAAQ,WAAW,IAAI;IAE/C,SAAS,OAAO,GAAG,EAAE,GAAG,EAAE,KAAK;QAC7B,OAAO,cAAc,CAAC,KAAK,KAAK;YAC9B,OAAO;YACP,YAAY;YACZ,cAAc;YACd,UAAU;QACZ;QACA,OAAO,GAAG,CAAC,IAAI;IACjB;IACA,IAAI;QACF,0EAA0E;QAC1E,OAAO,CAAC,GAAG;IACb,EAAE,OAAO,KAAK;QACZ,SAAS,SAAS,GAAG,EAAE,GAAG,EAAE,KAAK;YAC/B,OAAO,GAAG,CAAC,IAAI,GAAG;QACpB;IACF;IAEA,SAAS,KAAK,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW;QAC/C,yGAAyG;QACzG,IAAI,iBAAiB,WAAW,QAAQ,SAAS,YAAY,YAAY,UAAU;QACnF,IAAI,YAAY,OAAO,MAAM,CAAC,eAAe,SAAS;QACtD,IAAI,UAAU,IAAI,QAAQ,eAAe,EAAE;QAE3C,gEAAgE;QAChE,+BAA+B;QAC/B,eAAe,WAAW,WAAW;YAAE,OAAO,iBAAiB,SAAS,MAAM;QAAS;QAEvF,OAAO;IACT;IACA,QAAQ,IAAI,GAAG;IAEf,qEAAqE;IACrE,qEAAqE;IACrE,kEAAkE;IAClE,mEAAmE;IACnE,sEAAsE;IACtE,sEAAsE;IACtE,uEAAuE;IACvE,qEAAqE;IACrE,sEAAsE;IACtE,mEAAmE;IACnE,SAAS,SAAS,EAAE,EAAE,GAAG,EAAE,GAAG;QAC5B,IAAI;YACF,OAAO;gBAAE,MAAM;gBAAU,KAAK,GAAG,IAAI,CAAC,KAAK;YAAK;QAClD,EAAE,OAAO,KAAK;YACZ,OAAO;gBAAE,MAAM;gBAAS,KAAK;YAAI;QACnC;IACF;IAEA,IAAI,yBAAyB;IAC7B,IAAI,yBAAyB;IAC7B,IAAI,oBAAoB;IACxB,IAAI,oBAAoB;IAExB,gEAAgE;IAChE,iDAAiD;IACjD,IAAI,mBAAmB,CAAC;IAExB,kEAAkE;IAClE,wEAAwE;IACxE,oEAAoE;IACpE,2DAA2D;IAC3D,SAAS,aAAa;IACtB,SAAS,qBAAqB;IAC9B,SAAS,8BAA8B;IAEvC,mEAAmE;IACnE,6BAA6B;IAC7B,IAAI,oBAAoB,CAAC;IACzB,OAAO,mBAAmB,gBAAgB;QACxC,OAAO,IAAI;IACb;IAEA,IAAI,WAAW,OAAO,cAAc;IACpC,IAAI,0BAA0B,YAAY,SAAS,SAAS,OAAO,EAAE;IACrE,IAAI,2BACA,4BAA4B,MAC5B,OAAO,IAAI,CAAC,yBAAyB,iBAAiB;QACxD,oEAAoE;QACpE,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,IAAI,KAAK,2BAA2B,SAAS,GAC3C,UAAU,SAAS,GAAG,OAAO,MAAM,CAAC;IACtC,kBAAkB,SAAS,GAAG;IAC9B,eAAe,IAAI,eAAe;QAAE,OAAO;QAA4B,cAAc;IAAK;IAC1F,eACE,4BACA,eACA;QAAE,OAAO;QAAmB,cAAc;IAAK;IAEjD,kBAAkB,WAAW,GAAG,OAC9B,4BACA,mBACA;IAGF,oEAAoE;IACpE,2DAA2D;IAC3D,SAAS,sBAAsB,SAAS;QACtC;YAAC;YAAQ;YAAS;SAAS,CAAC,OAAO,CAAC,SAAS,MAAM;YACjD,OAAO,WAAW,QAAQ,SAAS,GAAG;gBACpC,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ;YAC9B;QACF;IACF;IAEA,QAAQ,mBAAmB,GAAG,SAAS,MAAM;QAC3C,IAAI,OAAO,OAAO,WAAW,cAAc,OAAO,WAAW;QAC7D,OAAO,OACH,SAAS,qBACT,gEAAgE;QAChE,qCAAqC;QACrC,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,MAAM,sBACpC;IACN;IAEA,QAAQ,IAAI,GAAG,SAAS,MAAM;QAC5B,IAAI,OAAO,cAAc,EAAE;YACzB,OAAO,cAAc,CAAC,QAAQ;QAChC,OAAO;YACL,OAAO,SAAS,GAAG;YACnB,OAAO,QAAQ,mBAAmB;QACpC;QACA,OAAO,SAAS,GAAG,OAAO,MAAM,CAAC;QACjC,OAAO;IACT;IAEA,qEAAqE;IACrE,oEAAoE;IACpE,uEAAuE;IACvE,uBAAuB;IACvB,QAAQ,KAAK,GAAG,SAAS,GAAG;QAC1B,OAAO;YAAE,SAAS;QAAI;IACxB;IAEA,SAAS,cAAc,SAAS,EAAE,WAAW;QAC3C,SAAS,OAAO,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;YAC1C,IAAI,SAAS,SAAS,SAAS,CAAC,OAAO,EAAE,WAAW;YACpD,IAAI,OAAO,IAAI,KAAK,SAAS;gBAC3B,OAAO,OAAO,GAAG;YACnB,OAAO;gBACL,IAAI,SAAS,OAAO,GAAG;gBACvB,IAAI,QAAQ,OAAO,KAAK;gBACxB,IAAI,SACA,OAAO,UAAU,YACjB,OAAO,IAAI,CAAC,OAAO,YAAY;oBACjC,OAAO,YAAY,OAAO,CAAC,MAAM,OAAO,EAAE,IAAI,CAAC,SAAS,KAAK;wBAC3D,OAAO,QAAQ,OAAO,SAAS;oBACjC,GAAG,SAAS,GAAG;wBACb,OAAO,SAAS,KAAK,SAAS;oBAChC;gBACF;gBAEA,OAAO,YAAY,OAAO,CAAC,OAAO,IAAI,CAAC,SAAS,SAAS;oBACvD,8DAA8D;oBAC9D,yDAAyD;oBACzD,qBAAqB;oBACrB,OAAO,KAAK,GAAG;oBACf,QAAQ;gBACV,GAAG,SAAS,KAAK;oBACf,8DAA8D;oBAC9D,gEAAgE;oBAChE,OAAO,OAAO,SAAS,OAAO,SAAS;gBACzC;YACF;QACF;QAEA,IAAI;QAEJ,SAAS,QAAQ,MAAM,EAAE,GAAG;YAC1B,SAAS;gBACP,OAAO,IAAI,YAAY,SAAS,OAAO,EAAE,MAAM;oBAC7C,OAAO,QAAQ,KAAK,SAAS;gBAC/B;YACF;YAEA,OAAO,kBACL,gEAAgE;YAChE,kEAAkE;YAClE,gEAAgE;YAChE,8DAA8D;YAC9D,kEAAkE;YAClE,iEAAiE;YACjE,gEAAgE;YAChE,2DAA2D;YAC3D,2DAA2D;YAC3D,iEAAiE;YACjE,iEAAiE;YACjE,6DAA6D;YAC7D,kBAAkB,gBAAgB,IAAI,CACpC,4BACA,2DAA2D;YAC3D,+BAA+B;YAC/B,8BACE;QACR;QAEA,oEAAoE;QACpE,mDAAmD;QACnD,eAAe,IAAI,EAAE,WAAW;YAAE,OAAO;QAAQ;IACnD;IAEA,sBAAsB,cAAc,SAAS;IAC7C,OAAO,cAAc,SAAS,EAAE,qBAAqB;QACnD,OAAO,IAAI;IACb;IACA,QAAQ,aAAa,GAAG;IAExB,6DAA6D;IAC7D,qEAAqE;IACrE,6CAA6C;IAC7C,QAAQ,KAAK,GAAG,SAAS,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW;QACvE,IAAI,gBAAgB,KAAK,GAAG,cAAc;QAE1C,IAAI,OAAO,IAAI,cACb,KAAK,SAAS,SAAS,MAAM,cAC7B;QAGF,OAAO,QAAQ,mBAAmB,CAAC,WAC/B,KAAK,uDAAuD;WAC5D,KAAK,IAAI,GAAG,IAAI,CAAC,SAAS,MAAM;YAC9B,OAAO,OAAO,IAAI,GAAG,OAAO,KAAK,GAAG,KAAK,IAAI;QAC/C;IACN;IAEA,SAAS,iBAAiB,OAAO,EAAE,IAAI,EAAE,OAAO;QAC9C,IAAI,QAAQ;QAEZ,OAAO,SAAS,OAAO,MAAM,EAAE,GAAG;YAChC,IAAI,UAAU,mBAAmB;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,UAAU,mBAAmB;gBAC/B,IAAI,WAAW,SAAS;oBACtB,MAAM;gBACR;gBAEA,4CAA4C;gBAC5C,4EAA4E;gBAC5E,OAAO;YACT;YAEA,QAAQ,MAAM,GAAG;YACjB,QAAQ,GAAG,GAAG;YAEd,MAAO,KAAM;gBACX,IAAI,WAAW,QAAQ,QAAQ;gBAC/B,IAAI,UAAU;oBACZ,IAAI,iBAAiB,oBAAoB,UAAU;oBACnD,IAAI,gBAAgB;wBAClB,IAAI,mBAAmB,kBAAkB;wBACzC,OAAO;oBACT;gBACF;gBAEA,IAAI,QAAQ,MAAM,KAAK,QAAQ;oBAC7B,sDAAsD;oBACtD,gCAAgC;oBAChC,QAAQ,IAAI,GAAG,QAAQ,KAAK,GAAG,QAAQ,GAAG;gBAE5C,OAAO,IAAI,QAAQ,MAAM,KAAK,SAAS;oBACrC,IAAI,UAAU,wBAAwB;wBACpC,QAAQ;wBACR,MAAM,QAAQ,GAAG;oBACnB;oBAEA,QAAQ,iBAAiB,CAAC,QAAQ,GAAG;gBAEvC,OAAO,IAAI,QAAQ,MAAM,KAAK,UAAU;oBACtC,QAAQ,MAAM,CAAC,UAAU,QAAQ,GAAG;gBACtC;gBAEA,QAAQ;gBAER,IAAI,SAAS,SAAS,SAAS,MAAM;gBACrC,IAAI,OAAO,IAAI,KAAK,UAAU;oBAC5B,6DAA6D;oBAC7D,0DAA0D;oBAC1D,QAAQ,QAAQ,IAAI,GAChB,oBACA;oBAEJ,IAAI,OAAO,GAAG,KAAK,kBAAkB;wBACnC;oBACF;oBAEA,OAAO;wBACL,OAAO,OAAO,GAAG;wBACjB,MAAM,QAAQ,IAAI;oBACpB;gBAEF,OAAO,IAAI,OAAO,IAAI,KAAK,SAAS;oBAClC,QAAQ;oBACR,uDAAuD;oBACvD,qDAAqD;oBACrD,QAAQ,MAAM,GAAG;oBACjB,QAAQ,GAAG,GAAG,OAAO,GAAG;gBAC1B;YACF;QACF;IACF;IAEA,qEAAqE;IACrE,gEAAgE;IAChE,qEAAqE;IACrE,wEAAwE;IACxE,SAAS,oBAAoB,QAAQ,EAAE,OAAO;QAC5C,IAAI,aAAa,QAAQ,MAAM;QAC/B,IAAI,SAAS,SAAS,QAAQ,CAAC,WAAW;QAC1C,IAAI,WAAW,WAAW;YACxB,+DAA+D;YAC/D,0DAA0D;YAC1D,eAAe;YACf,QAAQ,QAAQ,GAAG;YAEnB,+DAA+D;YAC/D,IAAI,eAAe,WAAW,SAAS,QAAQ,CAAC,SAAS,EAAE;gBACzD,0DAA0D;gBAC1D,sBAAsB;gBACtB,QAAQ,MAAM,GAAG;gBACjB,QAAQ,GAAG,GAAG;gBACd,oBAAoB,UAAU;gBAE9B,IAAI,QAAQ,MAAM,KAAK,SAAS;oBAC9B,8DAA8D;oBAC9D,8DAA8D;oBAC9D,OAAO;gBACT;YACF;YACA,IAAI,eAAe,UAAU;gBAC3B,QAAQ,MAAM,GAAG;gBACjB,QAAQ,GAAG,GAAG,IAAI,UAChB,sCAAsC,aAAa;YACvD;YAEA,OAAO;QACT;QAEA,IAAI,SAAS,SAAS,QAAQ,SAAS,QAAQ,EAAE,QAAQ,GAAG;QAE5D,IAAI,OAAO,IAAI,KAAK,SAAS;YAC3B,QAAQ,MAAM,GAAG;YACjB,QAAQ,GAAG,GAAG,OAAO,GAAG;YACxB,QAAQ,QAAQ,GAAG;YACnB,OAAO;QACT;QAEA,IAAI,OAAO,OAAO,GAAG;QAErB,IAAI,CAAE,MAAM;YACV,QAAQ,MAAM,GAAG;YACjB,QAAQ,GAAG,GAAG,IAAI,UAAU;YAC5B,QAAQ,QAAQ,GAAG;YACnB,OAAO;QACT;QAEA,IAAI,KAAK,IAAI,EAAE;YACb,8DAA8D;YAC9D,iEAAiE;YACjE,OAAO,CAAC,SAAS,UAAU,CAAC,GAAG,KAAK,KAAK;YAEzC,gEAAgE;YAChE,QAAQ,IAAI,GAAG,SAAS,OAAO;YAE/B,6DAA6D;YAC7D,0DAA0D;YAC1D,kEAAkE;YAClE,6DAA6D;YAC7D,+DAA+D;YAC/D,mBAAmB;YACnB,IAAI,QAAQ,MAAM,KAAK,UAAU;gBAC/B,QAAQ,MAAM,GAAG;gBACjB,QAAQ,GAAG,GAAG;YAChB;QAEF,OAAO;YACL,uDAAuD;YACvD,OAAO;QACT;QAEA,oEAAoE;QACpE,uBAAuB;QACvB,QAAQ,QAAQ,GAAG;QACnB,OAAO;IACT;IAEA,iEAAiE;IACjE,kCAAkC;IAClC,sBAAsB;IAEtB,OAAO,IAAI,mBAAmB;IAE9B,0EAA0E;IAC1E,6EAA6E;IAC7E,6EAA6E;IAC7E,8EAA8E;IAC9E,2EAA2E;IAC3E,OAAO,IAAI,gBAAgB;QACzB,OAAO,IAAI;IACb;IAEA,OAAO,IAAI,YAAY;QACrB,OAAO;IACT;IAEA,SAAS,aAAa,IAAI;QACxB,IAAI,QAAQ;YAAE,QAAQ,IAAI,CAAC,EAAE;QAAC;QAE9B,IAAI,KAAK,MAAM;YACb,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE;QAC1B;QAEA,IAAI,KAAK,MAAM;YACb,MAAM,UAAU,GAAG,IAAI,CAAC,EAAE;YAC1B,MAAM,QAAQ,GAAG,IAAI,CAAC,EAAE;QAC1B;QAEA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;IACvB;IAEA,SAAS,cAAc,KAAK;QAC1B,IAAI,SAAS,MAAM,UAAU,IAAI,CAAC;QAClC,OAAO,IAAI,GAAG;QACd,OAAO,OAAO,GAAG;QACjB,MAAM,UAAU,GAAG;IACrB;IAEA,SAAS,QAAQ,WAAW;QAC1B,qEAAqE;QACrE,mEAAmE;QACnE,uDAAuD;QACvD,IAAI,CAAC,UAAU,GAAG;YAAC;gBAAE,QAAQ;YAAO;SAAE;QACtC,YAAY,OAAO,CAAC,cAAc,IAAI;QACtC,IAAI,CAAC,KAAK,CAAC;IACb;IAEA,QAAQ,IAAI,GAAG,SAAS,GAAG;QACzB,IAAI,SAAS,OAAO;QACpB,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,OAAO,OAAQ;YACtB,KAAK,IAAI,CAAC;QACZ;QACA,KAAK,OAAO;QAEZ,8DAA8D;QAC9D,qDAAqD;QACrD,OAAO,SAAS;YACd,MAAO,KAAK,MAAM,CAAE;gBAClB,IAAI,MAAM,KAAK,GAAG;gBAClB,IAAI,OAAO,QAAQ;oBACjB,KAAK,KAAK,GAAG;oBACb,KAAK,IAAI,GAAG;oBACZ,OAAO;gBACT;YACF;YAEA,kEAAkE;YAClE,iEAAiE;YACjE,kEAAkE;YAClE,KAAK,IAAI,GAAG;YACZ,OAAO;QACT;IACF;IAEA,SAAS,OAAO,QAAQ;QACtB,IAAI,UAAU;YACZ,IAAI,iBAAiB,QAAQ,CAAC,eAAe;YAC7C,IAAI,gBAAgB;gBAClB,OAAO,eAAe,IAAI,CAAC;YAC7B;YAEA,IAAI,OAAO,SAAS,IAAI,KAAK,YAAY;gBACvC,OAAO;YACT;YAEA,IAAI,CAAC,MAAM,SAAS,MAAM,GAAG;gBAC3B,IAAI,IAAI,CAAC,GAAG,OAAO,SAAS;oBAC1B,MAAO,EAAE,IAAI,SAAS,MAAM,CAAE;wBAC5B,IAAI,OAAO,IAAI,CAAC,UAAU,IAAI;4BAC5B,KAAK,KAAK,GAAG,QAAQ,CAAC,EAAE;4BACxB,KAAK,IAAI,GAAG;4BACZ,OAAO;wBACT;oBACF;oBAEA,KAAK,KAAK,GAAG;oBACb,KAAK,IAAI,GAAG;oBAEZ,OAAO;gBACT;gBAEA,OAAO,KAAK,IAAI,GAAG;YACrB;QACF;QAEA,qCAAqC;QACrC,OAAO;YAAE,MAAM;QAAW;IAC5B;IACA,QAAQ,MAAM,GAAG;IAEjB,SAAS;QACP,OAAO;YAAE,OAAO;YAAW,MAAM;QAAK;IACxC;IAEA,QAAQ,SAAS,GAAG;QAClB,aAAa;QAEb,OAAO,SAAS,aAAa;YAC3B,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,IAAI,GAAG;YACZ,wDAAwD;YACxD,gCAAgC;YAChC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG;YACzB,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,QAAQ,GAAG;YAEhB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,GAAG,GAAG;YAEX,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;YAExB,IAAI,CAAC,eAAe;gBAClB,IAAK,IAAI,QAAQ,IAAI,CAAE;oBACrB,wDAAwD;oBACxD,IAAI,KAAK,MAAM,CAAC,OAAO,OACnB,OAAO,IAAI,CAAC,IAAI,EAAE,SAClB,CAAC,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK;wBAC1B,IAAI,CAAC,KAAK,GAAG;oBACf;gBACF;YACF;QACF;QAEA,MAAM;YACJ,IAAI,CAAC,IAAI,GAAG;YAEZ,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,EAAE;YAClC,IAAI,aAAa,UAAU,UAAU;YACrC,IAAI,WAAW,IAAI,KAAK,SAAS;gBAC/B,MAAM,WAAW,GAAG;YACtB;YAEA,OAAO,IAAI,CAAC,IAAI;QAClB;QAEA,mBAAmB,SAAS,SAAS;YACnC,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,MAAM;YACR;YAEA,IAAI,UAAU,IAAI;YAClB,SAAS,OAAO,GAAG,EAAE,MAAM;gBACzB,OAAO,IAAI,GAAG;gBACd,OAAO,GAAG,GAAG;gBACb,QAAQ,IAAI,GAAG;gBAEf,IAAI,QAAQ;oBACV,2DAA2D;oBAC3D,2DAA2D;oBAC3D,QAAQ,MAAM,GAAG;oBACjB,QAAQ,GAAG,GAAG;gBAChB;gBAEA,OAAO,CAAC,CAAE;YACZ;YAEA,IAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACpD,IAAI,QAAQ,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC9B,IAAI,SAAS,MAAM,UAAU;gBAE7B,IAAI,MAAM,MAAM,KAAK,QAAQ;oBAC3B,8DAA8D;oBAC9D,4DAA4D;oBAC5D,uBAAuB;oBACvB,OAAO,OAAO;gBAChB;gBAEA,IAAI,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;oBAC7B,IAAI,WAAW,OAAO,IAAI,CAAC,OAAO;oBAClC,IAAI,aAAa,OAAO,IAAI,CAAC,OAAO;oBAEpC,IAAI,YAAY,YAAY;wBAC1B,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,QAAQ,EAAE;4BAC9B,OAAO,OAAO,MAAM,QAAQ,EAAE;wBAChC,OAAO,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,UAAU,EAAE;4BACvC,OAAO,OAAO,MAAM,UAAU;wBAChC;oBAEF,OAAO,IAAI,UAAU;wBACnB,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,QAAQ,EAAE;4BAC9B,OAAO,OAAO,MAAM,QAAQ,EAAE;wBAChC;oBAEF,OAAO,IAAI,YAAY;wBACrB,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,UAAU,EAAE;4BAChC,OAAO,OAAO,MAAM,UAAU;wBAChC;oBAEF,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF;YACF;QACF;QAEA,QAAQ,SAAS,IAAI,EAAE,GAAG;YACxB,IAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACpD,IAAI,QAAQ,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC9B,IAAI,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,IACzB,OAAO,IAAI,CAAC,OAAO,iBACnB,IAAI,CAAC,IAAI,GAAG,MAAM,UAAU,EAAE;oBAChC,IAAI,eAAe;oBACnB;gBACF;YACF;YAEA,IAAI,gBACA,CAAC,SAAS,WACT,SAAS,UAAU,KACpB,aAAa,MAAM,IAAI,OACvB,OAAO,aAAa,UAAU,EAAE;gBAClC,0DAA0D;gBAC1D,wCAAwC;gBACxC,eAAe;YACjB;YAEA,IAAI,SAAS,eAAe,aAAa,UAAU,GAAG,CAAC;YACvD,OAAO,IAAI,GAAG;YACd,OAAO,GAAG,GAAG;YAEb,IAAI,cAAc;gBAChB,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,IAAI,GAAG,aAAa,UAAU;gBACnC,OAAO;YACT;YAEA,OAAO,IAAI,CAAC,QAAQ,CAAC;QACvB;QAEA,UAAU,SAAS,MAAM,EAAE,QAAQ;YACjC,IAAI,OAAO,IAAI,KAAK,SAAS;gBAC3B,MAAM,OAAO,GAAG;YAClB;YAEA,IAAI,OAAO,IAAI,KAAK,WAChB,OAAO,IAAI,KAAK,YAAY;gBAC9B,IAAI,CAAC,IAAI,GAAG,OAAO,GAAG;YACxB,OAAO,IAAI,OAAO,IAAI,KAAK,UAAU;gBACnC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG;gBACjC,IAAI,CAAC,MAAM,GAAG;gBACd,IAAI,CAAC,IAAI,GAAG;YACd,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY,UAAU;gBAC/C,IAAI,CAAC,IAAI,GAAG;YACd;YAEA,OAAO;QACT;QAEA,QAAQ,SAAS,UAAU;YACzB,IAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACpD,IAAI,QAAQ,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC9B,IAAI,MAAM,UAAU,KAAK,YAAY;oBACnC,IAAI,CAAC,QAAQ,CAAC,MAAM,UAAU,EAAE,MAAM,QAAQ;oBAC9C,cAAc;oBACd,OAAO;gBACT;YACF;QACF;QAEA,SAAS,SAAS,MAAM;YACtB,IAAK,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,EAAE,EAAG;gBACpD,IAAI,QAAQ,IAAI,CAAC,UAAU,CAAC,EAAE;gBAC9B,IAAI,MAAM,MAAM,KAAK,QAAQ;oBAC3B,IAAI,SAAS,MAAM,UAAU;oBAC7B,IAAI,OAAO,IAAI,KAAK,SAAS;wBAC3B,IAAI,SAAS,OAAO,GAAG;wBACvB,cAAc;oBAChB;oBACA,OAAO;gBACT;YACF;YAEA,+DAA+D;YAC/D,oDAAoD;YACpD,MAAM,IAAI,MAAM;QAClB;QAEA,eAAe,SAAS,QAAQ,EAAE,UAAU,EAAE,OAAO;YACnD,IAAI,CAAC,QAAQ,GAAG;gBACd,UAAU,OAAO;gBACjB,YAAY;gBACZ,SAAS;YACX;YAEA,IAAI,IAAI,CAAC,MAAM,KAAK,QAAQ;gBAC1B,2DAA2D;gBAC3D,2CAA2C;gBAC3C,IAAI,CAAC,GAAG,GAAG;YACb;YAEA,OAAO;QACT;IACF;IAEA,sEAAsE;IACtE,wEAAwE;IACxE,wEAAwE;IACxE,oEAAoE;IACpE,OAAO;AAET,EACE,uEAAuE;AACvE,oEAAoE;AACpE,sEAAsE;AACtE,2DAA2D;AAC3D,uCAA6B,OAAO,OAAO;AAG7C,IAAI;IACF,qBAAqB;AACvB,EAAE,OAAO,sBAAsB;IAC7B,iEAAiE;IACjE,wEAAwE;IACxE,yEAAyE;IACzE,sEAAsE;IACtE,wEAAwE;IACxE,wEAAwE;IACxE,uEAAuE;IACvE,uEAAuE;IACvE,wEAAwE;IACxE,qEAAqE;IACrE,IAAI,OAAO,eAAe,UAAU;QAClC,WAAW,kBAAkB,GAAG;IAClC,OAAO;QACL,SAAS,KAAK,0BAA0B;IAC1C;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2390, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/utils/getId.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (prefix, cnt) => (\n  `${prefix}-${cnt}-${Math.random().toString(16).slice(3, 8)}`\n);\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG,CAAC,QAAQ,MACxB,GAAG,OAAO,CAAC,EAAE,IAAI,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/createJob.js"], "sourcesContent": ["'use strict';\n\nconst getId = require('./utils/getId');\n\nlet jobCounter = 0;\n\nmodule.exports = ({\n  id: _id,\n  action,\n  payload = {},\n}) => {\n  let id = _id;\n  if (typeof id === 'undefined') {\n    id = getId('Job', jobCounter);\n    jobCounter += 1;\n  }\n\n  return {\n    id,\n    action,\n    payload,\n  };\n};\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AAEN,IAAI,aAAa;AAEjB,OAAO,OAAO,GAAG,CAAC,EAChB,IAAI,GAAG,EACP,MAAM,EACN,UAAU,CAAC,CAAC,EACb;IACC,IAAI,KAAK;IACT,IAAI,OAAO,OAAO,aAAa;QAC7B,KAAK,MAAM,OAAO;QAClB,cAAc;IAChB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2417, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/utils/log.js"], "sourcesContent": ["'use strict';\n\nlet logging = false;\n\nexports.logging = logging;\n\nexports.setLogging = (_logging) => {\n  logging = _logging;\n};\n\nexports.log = (...args) => (logging ? console.log.apply(this, args) : null);\n"], "names": [], "mappings": "AAAA;AAEA,IAAI,UAAU;AAEd,QAAQ,OAAO,GAAG;AAElB,QAAQ,UAAU,GAAG,CAAC;IACpB,UAAU;AACZ;AAEA,QAAQ,GAAG,GAAG,CAAC,GAAG,OAAU,UAAU,QAAQ,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2429, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/createScheduler.js"], "sourcesContent": ["'use strict';\n\nconst createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\n\nlet schedulerCounter = 0;\n\nmodule.exports = () => {\n  const id = getId('Scheduler', schedulerCounter);\n  const workers = {};\n  const runningWorkers = {};\n  let jobQueue = [];\n\n  schedulerCounter += 1;\n\n  const getQueueLen = () => jobQueue.length;\n  const getNumWorkers = () => Object.keys(workers).length;\n\n  const dequeue = () => {\n    if (jobQueue.length !== 0) {\n      const wIds = Object.keys(workers);\n      for (let i = 0; i < wIds.length; i += 1) {\n        if (typeof runningWorkers[wIds[i]] === 'undefined') {\n          jobQueue[0](workers[wIds[i]]);\n          break;\n        }\n      }\n    }\n  };\n\n  const queue = (action, payload) => (\n    new Promise((resolve, reject) => {\n      const job = createJob({ action, payload });\n      jobQueue.push(async (w) => {\n        jobQueue.shift();\n        runningWorkers[w.id] = job;\n        try {\n          resolve(await w[action].apply(this, [...payload, job.id]));\n        } catch (err) {\n          reject(err);\n        } finally {\n          delete runningWorkers[w.id];\n          dequeue();\n        }\n      });\n      log(`[${id}]: Add ${job.id} to JobQueue`);\n      log(`[${id}]: JobQueue length=${jobQueue.length}`);\n      dequeue();\n    })\n  );\n\n  const addWorker = (w) => {\n    workers[w.id] = w;\n    log(`[${id}]: Add ${w.id}`);\n    log(`[${id}]: Number of workers=${getNumWorkers()}`);\n    dequeue();\n    return w.id;\n  };\n\n  const addJob = async (action, ...payload) => {\n    if (getNumWorkers() === 0) {\n      throw Error(`[${id}]: You need to have at least one worker before adding jobs`);\n    }\n    return queue(action, payload);\n  };\n\n  const terminate = async () => {\n    Object.keys(workers).forEach(async (wid) => {\n      await workers[wid].terminate();\n    });\n    jobQueue = [];\n  };\n\n  return {\n    addWorker,\n    addJob,\n    terminate,\n    getQueueLen,\n    getNumWorkers,\n  };\n};\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM,EAAE,GAAG,EAAE;AACb,MAAM;AAEN,IAAI,mBAAmB;AAEvB,OAAO,OAAO,GAAG;IACf,MAAM,KAAK,MAAM,aAAa;IAC9B,MAAM,UAAU,CAAC;IACjB,MAAM,iBAAiB,CAAC;IACxB,IAAI,WAAW,EAAE;IAEjB,oBAAoB;IAEpB,MAAM,cAAc,IAAM,SAAS,MAAM;IACzC,MAAM,gBAAgB,IAAM,OAAO,IAAI,CAAC,SAAS,MAAM;IAEvD,MAAM,UAAU;QACd,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,MAAM,OAAO,OAAO,IAAI,CAAC;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;gBACvC,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,aAAa;oBAClD,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC5B;gBACF;YACF;QACF;IACF;IAEA,MAAM,QAAQ,CAAC,QAAQ,UACrB,IAAI,QAAQ,CAAC,SAAS;YACpB,MAAM,MAAM,UAAU;gBAAE;gBAAQ;YAAQ;YACxC,SAAS,IAAI,CAAC,OAAO;gBACnB,SAAS,KAAK;gBACd,cAAc,CAAC,EAAE,EAAE,CAAC,GAAG;gBACvB,IAAI;oBACF,QAAQ,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;2BAAI;wBAAS,IAAI,EAAE;qBAAC;gBAC1D,EAAE,OAAO,KAAK;oBACZ,OAAO;gBACT,SAAU;oBACR,OAAO,cAAc,CAAC,EAAE,EAAE,CAAC;oBAC3B;gBACF;YACF;YACA,IAAI,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,IAAI,EAAE,CAAC,YAAY,CAAC;YACxC,IAAI,CAAC,CAAC,EAAE,GAAG,mBAAmB,EAAE,SAAS,MAAM,EAAE;YACjD;QACF;IAGF,MAAM,YAAY,CAAC;QACjB,OAAO,CAAC,EAAE,EAAE,CAAC,GAAG;QAChB,IAAI,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,EAAE;QAC1B,IAAI,CAAC,CAAC,EAAE,GAAG,qBAAqB,EAAE,iBAAiB;QACnD;QACA,OAAO,EAAE,EAAE;IACb;IAEA,MAAM,SAAS,OAAO,QAAQ,GAAG;QAC/B,IAAI,oBAAoB,GAAG;YACzB,MAAM,MAAM,CAAC,CAAC,EAAE,GAAG,0DAA0D,CAAC;QAChF;QACA,OAAO,MAAM,QAAQ;IACvB;IAEA,MAAM,YAAY;QAChB,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,OAAO;YAClC,MAAM,OAAO,CAAC,IAAI,CAAC,SAAS;QAC9B;QACA,WAAW,EAAE;IACf;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2509, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/utils/getEnvironment.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (key) => {\n  const env = {};\n\n  if (typeof WorkerGlobalScope !== 'undefined') {\n    env.type = 'webworker';\n  } else if (typeof document === 'object') {\n    env.type = 'browser';\n  } else if (typeof process === 'object' && typeof require === 'function') {\n    env.type = 'node';\n  }\n\n  if (typeof key === 'undefined') {\n    return env;\n  }\n\n  return env[key];\n};\n"], "names": [], "mappings": "AASoB;AATpB;AAEA,OAAO,OAAO,GAAG,CAAC;IAChB,MAAM,MAAM,CAAC;IAEb,IAAI,OAAO,sBAAsB,aAAa;QAC5C,IAAI,IAAI,GAAG;IACb,OAAO,IAAI,OAAO,aAAa,UAAU;QACvC,IAAI,IAAI,GAAG;IACb,OAAO,IAAI,OAAO,gKAAA,CAAA,UAAO,KAAK,YAAY,iDAAmB,YAAY;QACvE,IAAI,IAAI,GAAG;IACb;IAEA,IAAI,OAAO,QAAQ,aAAa;QAC9B,OAAO;IACT;IAEA,OAAO,GAAG,CAAC,IAAI;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2530, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/utils/resolvePaths.js"], "sourcesContent": ["'use strict';\n\nconst isBrowser = require('./getEnvironment')('type') === 'browser';\n\nconst resolveURL = isBrowser ? s => (new URL(s, window.location.href)).href : s => s; // eslint-disable-line\n\nmodule.exports = (options) => {\n  const opts = { ...options };\n  ['corePath', 'workerPath', 'langPath'].forEach((key) => {\n    if (options[key]) {\n      opts[key] = resolveURL(opts[key]);\n    }\n  });\n  return opts;\n};\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,YAAY,qHAA4B,YAAY;AAE1D,MAAM,aAAa,YAAY,CAAA,IAAK,AAAC,IAAI,IAAI,GAAG,OAAO,QAAQ,CAAC,IAAI,EAAG,IAAI,GAAG,CAAA,IAAK,GAAG,sBAAsB;AAE5G,OAAO,OAAO,GAAG,CAAC;IAChB,MAAM,OAAO;QAAE,GAAG,OAAO;IAAC;IAC1B;QAAC;QAAY;QAAc;KAAW,CAAC,OAAO,CAAC,CAAC;QAC9C,IAAI,OAAO,CAAC,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,GAAG,WAAW,IAAI,CAAC,IAAI;QAClC;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2553, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/constants/OEM.js"], "sourcesContent": ["'use strict';\n\n/*\n * OEM = OCR Engine Mode, and there are 4 possible modes.\n *\n * By default tesseract.js uses LSTM_ONLY mode.\n *\n */\nmodule.exports = {\n  TESSERACT_ONLY: 0,\n  LSTM_ONLY: 1,\n  TESSERACT_LSTM_COMBINED: 2,\n  DEFAULT: 3,\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;;;CAKC,GACD,OAAO,OAAO,GAAG;IACf,gBAAgB;IAChB,WAAW;IACX,yBAAyB;IACzB,SAAS;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2575, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/constants/defaultOptions.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  /*\n   * Use BlobURL for worker script by default\n   * TODO: remove this option\n   *\n   */\n  workerBlobURL: true,\n  logger: () => {},\n};\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG;IACf;;;;GAIC,GACD,eAAe;IACf,QAAQ,KAAO;AACjB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2589, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/worker/browser/defaultOptions.js"], "sourcesContent": ["'use strict';\n\nconst version = require('../../../package.json').version;\nconst defaultOptions = require('../../constants/defaultOptions');\n\n/*\n * Default options for browser worker\n */\nmodule.exports = {\n  ...defaultOptions,\n  workerPath: `https://cdn.jsdelivr.net/npm/tesseract.js@v${version}/dist/worker.min.js`,\n};\n"], "names": [], "mappings": "AAAA;AAEA,MAAM,UAAU,mFAAiC,OAAO;AACxD,MAAM;AAEN;;CAEC,GACD,OAAO,OAAO,GAAG;IACf,GAAG,cAAc;IACjB,YAAY,CAAC,2CAA2C,EAAE,QAAQ,mBAAmB,CAAC;AACxF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2603, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/worker/browser/spawnWorker.js"], "sourcesContent": ["'use strict';\n\n/**\n * spawnWorker\n *\n * @name spawnWorker\n * @function create a new Worker in browser\n * @access public\n */\nmodule.exports = ({ workerPath, workerBlobURL }) => {\n  let worker;\n  if (Blob && URL && workerBlobURL) {\n    const blob = new Blob([`importScripts(\"${workerPath}\");`], {\n      type: 'application/javascript',\n    });\n    worker = new Worker(URL.createObjectURL(blob));\n  } else {\n    worker = new Worker(workerPath);\n  }\n\n  return worker;\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;CAMC,GACD,OAAO,OAAO,GAAG,CAAC,EAAE,UAAU,EAAE,aAAa,EAAE;IAC7C,IAAI;IACJ,IAAI,QAAQ,OAAO,eAAe;QAChC,MAAM,OAAO,IAAI,KAAK;YAAC,CAAC,eAAe,EAAE,WAAW,GAAG,CAAC;SAAC,EAAE;YACzD,MAAM;QACR;QACA,SAAS,IAAI,OAAO,IAAI,eAAe,CAAC;IAC1C,OAAO;QACL,SAAS,IAAI,OAAO;IACtB;IAEA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2629, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/worker/browser/terminateWorker.js"], "sourcesContent": ["'use strict';\n\n/**\n * terminateWorker\n *\n * @name terminateWorker\n * @function terminate worker\n * @access public\n */\nmodule.exports = (worker) => {\n  worker.terminate();\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;CAMC,GACD,OAAO,OAAO,GAAG,CAAC;IAChB,OAAO,SAAS;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2644, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/worker/browser/onMessage.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = (worker, handler) => {\n  worker.onmessage = ({ data }) => { // eslint-disable-line\n    handler(data);\n  };\n};\n"], "names": [], "mappings": "AAAA;AAEA,OAAO,OAAO,GAAG,CAAC,QAAQ;IACxB,OAAO,SAAS,GAAG,CAAC,EAAE,IAAI,EAAE;QAC1B,QAAQ;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2655, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/worker/browser/send.js"], "sourcesContent": ["'use strict';\n\n/**\n * send\n *\n * @name send\n * @function send packet to worker and create a job\n * @access public\n */\nmodule.exports = async (worker, packet) => {\n  worker.postMessage(packet);\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;CAMC,GACD,OAAO,OAAO,GAAG,OAAO,QAAQ;IAC9B,OAAO,WAAW,CAAC;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2670, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/worker/browser/loadImage.js"], "sourcesContent": ["'use strict';\n\n/**\n * readFromBlobOrFile\n *\n * @name readFromBlobOrFile\n * @function\n * @access private\n */\nconst readFromBlobOrFile = (blob) => (\n  new Promise((resolve, reject) => {\n    const fileReader = new FileReader();\n    fileReader.onload = () => {\n      resolve(fileReader.result);\n    };\n    fileReader.onerror = ({ target: { error: { code } } }) => {\n      reject(Error(`File could not be read! Code=${code}`));\n    };\n    fileReader.readAsArrayBuffer(blob);\n  })\n);\n\n/**\n * loadImage\n *\n * @name loadImage\n * @function load image from different source\n * @access private\n */\nconst loadImage = async (image) => {\n  let data = image;\n  if (typeof image === 'undefined') {\n    return 'undefined';\n  }\n\n  if (typeof image === 'string') {\n    // Base64 Image\n    if (/data:image\\/([a-zA-Z]*);base64,([^\"]*)/.test(image)) {\n      data = atob(image.split(',')[1])\n        .split('')\n        .map((c) => c.charCodeAt(0));\n    } else {\n      const resp = await fetch(image);\n      data = await resp.arrayBuffer();\n    }\n  } else if (typeof HTMLElement !== 'undefined' && image instanceof HTMLElement) {\n    if (image.tagName === 'IMG') {\n      data = await loadImage(image.src);\n    }\n    if (image.tagName === 'VIDEO') {\n      data = await loadImage(image.poster);\n    }\n    if (image.tagName === 'CANVAS') {\n      await new Promise((resolve) => {\n        image.toBlob(async (blob) => {\n          data = await readFromBlobOrFile(blob);\n          resolve();\n        });\n      });\n    }\n  } else if (typeof OffscreenCanvas !== 'undefined' && image instanceof OffscreenCanvas) {\n    const blob = await image.convertToBlob();\n    data = await readFromBlobOrFile(blob);\n  } else if (image instanceof File || image instanceof Blob) {\n    data = await readFromBlobOrFile(image);\n  }\n\n  return new Uint8Array(data);\n};\n\nmodule.exports = loadImage;\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;CAMC,GACD,MAAM,qBAAqB,CAAC,OAC1B,IAAI,QAAQ,CAAC,SAAS;QACpB,MAAM,aAAa,IAAI;QACvB,WAAW,MAAM,GAAG;YAClB,QAAQ,WAAW,MAAM;QAC3B;QACA,WAAW,OAAO,GAAG,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;YACnD,OAAO,MAAM,CAAC,6BAA6B,EAAE,MAAM;QACrD;QACA,WAAW,iBAAiB,CAAC;IAC/B;AAGF;;;;;;CAMC,GACD,MAAM,YAAY,OAAO;IACvB,IAAI,OAAO;IACX,IAAI,OAAO,UAAU,aAAa;QAChC,OAAO;IACT;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,eAAe;QACf,IAAI,yCAAyC,IAAI,CAAC,QAAQ;YACxD,OAAO,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC,EAAE,EAC5B,KAAK,CAAC,IACN,GAAG,CAAC,CAAC,IAAM,EAAE,UAAU,CAAC;QAC7B,OAAO;YACL,MAAM,OAAO,MAAM,MAAM;YACzB,OAAO,MAAM,KAAK,WAAW;QAC/B;IACF,OAAO,IAAI,OAAO,gBAAgB,eAAe,iBAAiB,aAAa;QAC7E,IAAI,MAAM,OAAO,KAAK,OAAO;YAC3B,OAAO,MAAM,UAAU,MAAM,GAAG;QAClC;QACA,IAAI,MAAM,OAAO,KAAK,SAAS;YAC7B,OAAO,MAAM,UAAU,MAAM,MAAM;QACrC;QACA,IAAI,MAAM,OAAO,KAAK,UAAU;YAC9B,MAAM,IAAI,QAAQ,CAAC;gBACjB,MAAM,MAAM,CAAC,OAAO;oBAClB,OAAO,MAAM,mBAAmB;oBAChC;gBACF;YACF;QACF;IACF,OAAO,IAAI,OAAO,oBAAoB,eAAe,iBAAiB,iBAAiB;QACrF,MAAM,OAAO,MAAM,MAAM,aAAa;QACtC,OAAO,MAAM,mBAAmB;IAClC,OAAO,IAAI,iBAAiB,QAAQ,iBAAiB,MAAM;QACzD,OAAO,MAAM,mBAAmB;IAClC;IAEA,OAAO,IAAI,WAAW;AACxB;AAEA,OAAO,OAAO,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2735, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/worker/browser/index.js"], "sourcesContent": ["'use strict';\n\n/**\n *\n * Tesseract Worker adapter for browser\n *\n * @fileoverview Tesseract Worker adapter for browser\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nconst defaultOptions = require('./defaultOptions');\nconst spawnWorker = require('./spawnWorker');\nconst terminateWorker = require('./terminateWorker');\nconst onMessage = require('./onMessage');\nconst send = require('./send');\nconst loadImage = require('./loadImage');\n\nmodule.exports = {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  send,\n  loadImage,\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;;;CAQC,GACD,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AAEN,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2763, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/createWorker.js"], "sourcesContent": ["'use strict';\n\nconst resolvePaths = require('./utils/resolvePaths');\nconst createJob = require('./createJob');\nconst { log } = require('./utils/log');\nconst getId = require('./utils/getId');\nconst OEM = require('./constants/OEM');\nconst {\n  defaultOptions,\n  spawnWorker,\n  terminateWorker,\n  onMessage,\n  loadImage,\n  send,\n} = require('./worker/node');\n\nlet workerCounter = 0;\n\nmodule.exports = async (langs = 'eng', oem = OEM.LSTM_ONLY, _options = {}, config = {}) => {\n  const id = getId('Worker', workerCounter);\n  const {\n    logger,\n    errorHandler,\n    ...options\n  } = resolvePaths({\n    ...defaultOptions,\n    ..._options,\n  });\n  const promises = {};\n\n  // Current langs, oem, and config file.\n  // Used if the user ever re-initializes the worker using `worker.reinitialize`.\n  const currentLangs = typeof langs === 'string' ? langs.split('+') : langs;\n  let currentOem = oem;\n  let currentConfig = config;\n  const lstmOnlyCore = [OEM.DEFAULT, OEM.LSTM_ONLY].includes(oem) && !options.legacyCore;\n\n  let workerResReject;\n  let workerResResolve;\n  const workerRes = new Promise((resolve, reject) => {\n    workerResResolve = resolve;\n    workerResReject = reject;\n  });\n  const workerError = (event) => { workerResReject(event.message); };\n\n  let worker = spawnWorker(options);\n  worker.onerror = workerError;\n\n  workerCounter += 1;\n\n  const startJob = ({ id: jobId, action, payload }) => (\n    new Promise((resolve, reject) => {\n      log(`[${id}]: Start ${jobId}, action=${action}`);\n      // Using both `action` and `jobId` in case user provides non-unique `jobId`.\n      const promiseId = `${action}-${jobId}`;\n      promises[promiseId] = { resolve, reject };\n      send(worker, {\n        workerId: id,\n        jobId,\n        action,\n        payload,\n      });\n    })\n  );\n\n  const load = () => (\n    console.warn('`load` is depreciated and should be removed from code (workers now come pre-loaded)')\n  );\n\n  const loadInternal = (jobId) => (\n    startJob(createJob({\n      id: jobId, action: 'load', payload: { options: { lstmOnly: lstmOnlyCore, corePath: options.corePath, logging: options.logging } },\n    }))\n  );\n\n  const writeText = (path, text, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'writeFile', args: [path, text] },\n    }))\n  );\n\n  const readText = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'readFile', args: [path, { encoding: 'utf8' }] },\n    }))\n  );\n\n  const removeFile = (path, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method: 'unlink', args: [path] },\n    }))\n  );\n\n  const FS = (method, args, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'FS',\n      payload: { method, args },\n    }))\n  );\n\n  const loadLanguageInternal = (_langs, jobId) => startJob(createJob({\n    id: jobId,\n    action: 'loadLanguage',\n    payload: {\n      langs: _langs,\n      options: {\n        langPath: options.langPath,\n        dataPath: options.dataPath,\n        cachePath: options.cachePath,\n        cacheMethod: options.cacheMethod,\n        gzip: options.gzip,\n        lstmOnly: [OEM.DEFAULT, OEM.LSTM_ONLY].includes(currentOem)\n          && !options.legacyLang,\n      },\n    },\n  }));\n\n  const initializeInternal = (_langs, _oem, _config, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'initialize',\n      payload: { langs: _langs, oem: _oem, config: _config },\n    }))\n  );\n\n  const reinitialize = (langs = 'eng', oem, config, jobId) => { // eslint-disable-line\n\n    if (lstmOnlyCore && [OEM.TESSERACT_ONLY, OEM.TESSERACT_LSTM_COMBINED].includes(oem)) throw Error('Legacy model requested but code missing.');\n\n    const _oem = oem || currentOem;\n    currentOem = _oem;\n\n    const _config = config || currentConfig;\n    currentConfig = _config;\n\n    // Only load langs that are not already loaded.\n    // This logic fails if the user downloaded the LSTM-only English data for a language\n    // and then uses `worker.reinitialize` to switch to the Legacy engine.\n    // However, the correct data will still be downloaded after initialization fails\n    // and this can be avoided entirely if the user loads the correct data ahead of time.\n    const langsArr = typeof langs === 'string' ? langs.split('+') : langs;\n    const _langs = langsArr.filter((x) => !currentLangs.includes(x));\n    currentLangs.push(..._langs);\n\n    if (_langs.length > 0) {\n      return loadLanguageInternal(_langs, jobId)\n        .then(() => initializeInternal(langs, _oem, _config, jobId));\n    }\n\n    return initializeInternal(langs, _oem, _config, jobId);\n  };\n\n  const setParameters = (params = {}, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'setParameters',\n      payload: { params },\n    }))\n  );\n\n  const recognize = async (image, opts = {}, output = {\n    text: true,\n  }, jobId) => (\n    startJob(createJob({\n      id: jobId,\n      action: 'recognize',\n      payload: { image: await loadImage(image), options: opts, output },\n    }))\n  );\n\n  const detect = async (image, jobId) => {\n    if (lstmOnlyCore) throw Error('`worker.detect` requires Legacy model, which was not loaded.');\n\n    return startJob(createJob({\n      id: jobId,\n      action: 'detect',\n      payload: { image: await loadImage(image) },\n    }));\n  };\n\n  const terminate = async () => {\n    if (worker !== null) {\n      /*\n      await startJob(createJob({\n        id: jobId,\n        action: 'terminate',\n      }));\n      */\n      terminateWorker(worker);\n      worker = null;\n    }\n    return Promise.resolve();\n  };\n\n  onMessage(worker, ({\n    workerId, jobId, status, action, data,\n  }) => {\n    const promiseId = `${action}-${jobId}`;\n    if (status === 'resolve') {\n      log(`[${workerId}]: Complete ${jobId}`);\n      promises[promiseId].resolve({ jobId, data });\n      delete promises[promiseId];\n    } else if (status === 'reject') {\n      promises[promiseId].reject(data);\n      delete promises[promiseId];\n      if (action === 'load') workerResReject(data);\n      if (errorHandler) {\n        errorHandler(data);\n      } else {\n        throw Error(data);\n      }\n    } else if (status === 'progress') {\n      logger({ ...data, userJobId: jobId });\n    }\n  });\n\n  const resolveObj = {\n    id,\n    worker,\n    load,\n    writeText,\n    readText,\n    removeFile,\n    FS,\n    reinitialize,\n    setParameters,\n    recognize,\n    detect,\n    terminate,\n  };\n\n  loadInternal()\n    .then(() => loadLanguageInternal(langs))\n    .then(() => initializeInternal(langs, oem, config))\n    .then(() => workerResResolve(resolveObj))\n    .catch(() => {});\n\n  return workerRes;\n};\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AACN,MAAM;AACN,MAAM,EAAE,GAAG,EAAE;AACb,MAAM;AACN,MAAM;AACN,MAAM,EACJ,cAAc,EACd,WAAW,EACX,eAAe,EACf,SAAS,EACT,SAAS,EACT,IAAI,EACL;AAED,IAAI,gBAAgB;AAEpB,OAAO,OAAO,GAAG,OAAO,QAAQ,KAAK,EAAE,MAAM,IAAI,SAAS,EAAE,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IACpF,MAAM,KAAK,MAAM,UAAU;IAC3B,MAAM,EACJ,MAAM,EACN,YAAY,EACZ,GAAG,SACJ,GAAG,aAAa;QACf,GAAG,cAAc;QACjB,GAAG,QAAQ;IACb;IACA,MAAM,WAAW,CAAC;IAElB,uCAAuC;IACvC,+EAA+E;IAC/E,MAAM,eAAe,OAAO,UAAU,WAAW,MAAM,KAAK,CAAC,OAAO;IACpE,IAAI,aAAa;IACjB,IAAI,gBAAgB;IACpB,MAAM,eAAe;QAAC,IAAI,OAAO;QAAE,IAAI,SAAS;KAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,UAAU;IAEtF,IAAI;IACJ,IAAI;IACJ,MAAM,YAAY,IAAI,QAAQ,CAAC,SAAS;QACtC,mBAAmB;QACnB,kBAAkB;IACpB;IACA,MAAM,cAAc,CAAC;QAAY,gBAAgB,MAAM,OAAO;IAAG;IAEjE,IAAI,SAAS,YAAY;IACzB,OAAO,OAAO,GAAG;IAEjB,iBAAiB;IAEjB,MAAM,WAAW,CAAC,EAAE,IAAI,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAC9C,IAAI,QAAQ,CAAC,SAAS;YACpB,IAAI,CAAC,CAAC,EAAE,GAAG,SAAS,EAAE,MAAM,SAAS,EAAE,QAAQ;YAC/C,4EAA4E;YAC5E,MAAM,YAAY,GAAG,OAAO,CAAC,EAAE,OAAO;YACtC,QAAQ,CAAC,UAAU,GAAG;gBAAE;gBAAS;YAAO;YACxC,KAAK,QAAQ;gBACX,UAAU;gBACV;gBACA;gBACA;YACF;QACF;IAGF,MAAM,OAAO,IACX,QAAQ,IAAI,CAAC;IAGf,MAAM,eAAe,CAAC,QACpB,SAAS,UAAU;YACjB,IAAI;YAAO,QAAQ;YAAQ,SAAS;gBAAE,SAAS;oBAAE,UAAU;oBAAc,UAAU,QAAQ,QAAQ;oBAAE,SAAS,QAAQ,OAAO;gBAAC;YAAE;QAClI;IAGF,MAAM,YAAY,CAAC,MAAM,MAAM,QAC7B,SAAS,UAAU;YACjB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE,QAAQ;gBAAa,MAAM;oBAAC;oBAAM;iBAAK;YAAC;QACrD;IAGF,MAAM,WAAW,CAAC,MAAM,QACtB,SAAS,UAAU;YACjB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE,QAAQ;gBAAY,MAAM;oBAAC;oBAAM;wBAAE,UAAU;oBAAO;iBAAE;YAAC;QACpE;IAGF,MAAM,aAAa,CAAC,MAAM,QACxB,SAAS,UAAU;YACjB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE,QAAQ;gBAAU,MAAM;oBAAC;iBAAK;YAAC;QAC5C;IAGF,MAAM,KAAK,CAAC,QAAQ,MAAM,QACxB,SAAS,UAAU;YACjB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE;gBAAQ;YAAK;QAC1B;IAGF,MAAM,uBAAuB,CAAC,QAAQ,QAAU,SAAS,UAAU;YACjE,IAAI;YACJ,QAAQ;YACR,SAAS;gBACP,OAAO;gBACP,SAAS;oBACP,UAAU,QAAQ,QAAQ;oBAC1B,UAAU,QAAQ,QAAQ;oBAC1B,WAAW,QAAQ,SAAS;oBAC5B,aAAa,QAAQ,WAAW;oBAChC,MAAM,QAAQ,IAAI;oBAClB,UAAU;wBAAC,IAAI,OAAO;wBAAE,IAAI,SAAS;qBAAC,CAAC,QAAQ,CAAC,eAC3C,CAAC,QAAQ,UAAU;gBAC1B;YACF;QACF;IAEA,MAAM,qBAAqB,CAAC,QAAQ,MAAM,SAAS,QACjD,SAAS,UAAU;YACjB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE,OAAO;gBAAQ,KAAK;gBAAM,QAAQ;YAAQ;QACvD;IAGF,MAAM,eAAe,CAAC,QAAQ,KAAK,EAAE,KAAK,QAAQ;QAEhD,IAAI,gBAAgB;YAAC,IAAI,cAAc;YAAE,IAAI,uBAAuB;SAAC,CAAC,QAAQ,CAAC,MAAM,MAAM,MAAM;QAEjG,MAAM,OAAO,OAAO;QACpB,aAAa;QAEb,MAAM,UAAU,UAAU;QAC1B,gBAAgB;QAEhB,+CAA+C;QAC/C,oFAAoF;QACpF,sEAAsE;QACtE,gFAAgF;QAChF,qFAAqF;QACrF,MAAM,WAAW,OAAO,UAAU,WAAW,MAAM,KAAK,CAAC,OAAO;QAChE,MAAM,SAAS,SAAS,MAAM,CAAC,CAAC,IAAM,CAAC,aAAa,QAAQ,CAAC;QAC7D,aAAa,IAAI,IAAI;QAErB,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,OAAO,qBAAqB,QAAQ,OACjC,IAAI,CAAC,IAAM,mBAAmB,OAAO,MAAM,SAAS;QACzD;QAEA,OAAO,mBAAmB,OAAO,MAAM,SAAS;IAClD;IAEA,MAAM,gBAAgB,CAAC,SAAS,CAAC,CAAC,EAAE,QAClC,SAAS,UAAU;YACjB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE;YAAO;QACpB;IAGF,MAAM,YAAY,OAAO,OAAO,OAAO,CAAC,CAAC,EAAE,SAAS;QAClD,MAAM;IACR,CAAC,EAAE,QACD,SAAS,UAAU;YACjB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE,OAAO,MAAM,UAAU;gBAAQ,SAAS;gBAAM;YAAO;QAClE;IAGF,MAAM,SAAS,OAAO,OAAO;QAC3B,IAAI,cAAc,MAAM,MAAM;QAE9B,OAAO,SAAS,UAAU;YACxB,IAAI;YACJ,QAAQ;YACR,SAAS;gBAAE,OAAO,MAAM,UAAU;YAAO;QAC3C;IACF;IAEA,MAAM,YAAY;QAChB,IAAI,WAAW,MAAM;YACnB;;;;;MAKA,GACA,gBAAgB;YAChB,SAAS;QACX;QACA,OAAO,QAAQ,OAAO;IACxB;IAEA,UAAU,QAAQ,CAAC,EACjB,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EACtC;QACC,MAAM,YAAY,GAAG,OAAO,CAAC,EAAE,OAAO;QACtC,IAAI,WAAW,WAAW;YACxB,IAAI,CAAC,CAAC,EAAE,SAAS,YAAY,EAAE,OAAO;YACtC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC;gBAAE;gBAAO;YAAK;YAC1C,OAAO,QAAQ,CAAC,UAAU;QAC5B,OAAO,IAAI,WAAW,UAAU;YAC9B,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC;YAC3B,OAAO,QAAQ,CAAC,UAAU;YAC1B,IAAI,WAAW,QAAQ,gBAAgB;YACvC,IAAI,cAAc;gBAChB,aAAa;YACf,OAAO;gBACL,MAAM,MAAM;YACd;QACF,OAAO,IAAI,WAAW,YAAY;YAChC,OAAO;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAM;QACrC;IACF;IAEA,MAAM,aAAa;QACjB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,eACG,IAAI,CAAC,IAAM,qBAAqB,QAChC,IAAI,CAAC,IAAM,mBAAmB,OAAO,KAAK,SAC1C,IAAI,CAAC,IAAM,iBAAiB,aAC5B,KAAK,CAAC,KAAO;IAEhB,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3004, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/Tesseract.js"], "sourcesContent": ["'use strict';\n\nconst createWorker = require('./createWorker');\n\nconst recognize = async (image, langs, options) => {\n  const worker = await createWorker(langs, 1, options);\n  return worker.recognize(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nconst detect = async (image, options) => {\n  const worker = await createWorker('osd', 0, options);\n  return worker.detect(image)\n    .finally(async () => {\n      await worker.terminate();\n    });\n};\n\nmodule.exports = {\n  recognize,\n  detect,\n};\n"], "names": [], "mappings": "AAAA;AAEA,MAAM;AAEN,MAAM,YAAY,OAAO,OAAO,OAAO;IACrC,MAAM,SAAS,MAAM,aAAa,OAAO,GAAG;IAC5C,OAAO,OAAO,SAAS,CAAC,OACrB,OAAO,CAAC;QACP,MAAM,OAAO,SAAS;IACxB;AACJ;AAEA,MAAM,SAAS,OAAO,OAAO;IAC3B,MAAM,SAAS,MAAM,aAAa,OAAO,GAAG;IAC5C,OAAO,OAAO,MAAM,CAAC,OAClB,OAAO,CAAC;QACP,MAAM,OAAO,SAAS;IACxB;AACJ;AAEA,OAAO,OAAO,GAAG;IACf;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3027, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/constants/languages.js"], "sourcesContent": ["'use strict';\n\n/*\n * languages with existing tesseract traineddata\n * https://tesseract-ocr.github.io/tessdoc/Data-Files#data-files-for-version-400-november-29-2016\n */\n\n/**\n * @typedef {object} Languages\n * @property {string} AFR Afrikaans\n * @property {string} AMH Amharic\n * @property {string} ARA Arabic\n * @property {string} ASM Assamese\n * @property {string} AZE Azerbaijani\n * @property {string} AZE_CYRL Azerbaijani - Cyrillic\n * @property {string} BEL Belarusian\n * @property {string} BEN Bengali\n * @property {string} BOD Tibetan\n * @property {string} BOS Bosnian\n * @property {string} BUL Bulgarian\n * @property {string} CAT Catalan; Valencian\n * @property {string} CEB Cebuano\n * @property {string} CES Czech\n * @property {string} CHI_SIM Chinese - Simplified\n * @property {string} CHI_TRA Chinese - Traditional\n * @property {string} CHR Cherokee\n * @property {string} CYM Welsh\n * @property {string} DAN Danish\n * @property {string} DEU German\n * @property {string} DZO Dzongkha\n * @property {string} ELL Greek, Modern (1453-)\n * @property {string} ENG English\n * @property {string} ENM English, Middle (1100-1500)\n * @property {string} EPO Esperanto\n * @property {string} EST Estonian\n * @property {string} EUS Basque\n * @property {string} FAS Persian\n * @property {string} FIN Finnish\n * @property {string} FRA French\n * @property {string} FRK German Fraktur\n * @property {string} FRM French, Middle (ca. 1400-1600)\n * @property {string} GLE Irish\n * @property {string} GLG Galician\n * @property {string} GRC Greek, Ancient (-1453)\n * @property {string} GUJ Gujarati\n * @property {string} HAT Haitian; Haitian Creole\n * @property {string} HEB Hebrew\n * @property {string} HIN Hindi\n * @property {string} HRV Croatian\n * @property {string} HUN Hungarian\n * @property {string} IKU Inuktitut\n * @property {string} IND Indonesian\n * @property {string} ISL Icelandic\n * @property {string} ITA Italian\n * @property {string} ITA_OLD Italian - Old\n * @property {string} JAV Javanese\n * @property {string} JPN Japanese\n * @property {string} KAN Kannada\n * @property {string} KAT Georgian\n * @property {string} KAT_OLD Georgian - Old\n * @property {string} KAZ Kazakh\n * @property {string} KHM Central Khmer\n * @property {string} KIR Kirghiz; Kyrgyz\n * @property {string} KOR Korean\n * @property {string} KUR Kurdish\n * @property {string} LAO Lao\n * @property {string} LAT Latin\n * @property {string} LAV Latvian\n * @property {string} LIT Lithuanian\n * @property {string} MAL Malayalam\n * @property {string} MAR Marathi\n * @property {string} MKD Macedonian\n * @property {string} MLT Maltese\n * @property {string} MSA Malay\n * @property {string} MYA Burmese\n * @property {string} NEP Nepali\n * @property {string} NLD Dutch; Flemish\n * @property {string} NOR Norwegian\n * @property {string} ORI Oriya\n * @property {string} PAN Panjabi; Punjabi\n * @property {string} POL Polish\n * @property {string} POR Portuguese\n * @property {string} PUS Pushto; Pashto\n * @property {string} RON Romanian; Moldavian; Moldovan\n * @property {string} RUS Russian\n * @property {string} SAN Sanskrit\n * @property {string} SIN Sinhala; Sinhalese\n * @property {string} SLK Slovak\n * @property {string} SLV Slovenian\n * @property {string} SPA Spanish; Castilian\n * @property {string} SPA_OLD Spanish; Castilian - Old\n * @property {string} SQI Albanian\n * @property {string} SRP Serbian\n * @property {string} SRP_LATN Serbian - Latin\n * @property {string} SWA Swahili\n * @property {string} SWE Swedish\n * @property {string} SYR Syriac\n * @property {string} TAM Tamil\n * @property {string} TEL Telugu\n * @property {string} TGK Tajik\n * @property {string} TGL Tagalog\n * @property {string} THA Thai\n * @property {string} TIR Tigrinya\n * @property {string} TUR Turkish\n * @property {string} UIG Uighur; Uyghur\n * @property {string} UKR Ukrainian\n * @property {string} URD Urdu\n * @property {string} UZB Uzbek\n * @property {string} UZB_CYRL Uzbek - Cyrillic\n * @property {string} VIE Vietnamese\n * @property {string} YID Yiddish\n */\n\n/**\n  * @type {Languages}\n  */\nmodule.exports = {\n  AFR: 'afr',\n  AMH: 'amh',\n  ARA: 'ara',\n  ASM: 'asm',\n  AZE: 'aze',\n  AZE_CYRL: 'aze_cyrl',\n  BEL: 'bel',\n  BEN: 'ben',\n  BOD: 'bod',\n  BOS: 'bos',\n  BUL: 'bul',\n  CAT: 'cat',\n  CEB: 'ceb',\n  CES: 'ces',\n  CHI_SIM: 'chi_sim',\n  CHI_TRA: 'chi_tra',\n  CHR: 'chr',\n  CYM: 'cym',\n  DAN: 'dan',\n  DEU: 'deu',\n  DZO: 'dzo',\n  ELL: 'ell',\n  ENG: 'eng',\n  ENM: 'enm',\n  EPO: 'epo',\n  EST: 'est',\n  EUS: 'eus',\n  FAS: 'fas',\n  FIN: 'fin',\n  FRA: 'fra',\n  FRK: 'frk',\n  FRM: 'frm',\n  GLE: 'gle',\n  GLG: 'glg',\n  GRC: 'grc',\n  GUJ: 'guj',\n  HAT: 'hat',\n  HEB: 'heb',\n  HIN: 'hin',\n  HRV: 'hrv',\n  HUN: 'hun',\n  IKU: 'iku',\n  IND: 'ind',\n  ISL: 'isl',\n  ITA: 'ita',\n  ITA_OLD: 'ita_old',\n  JAV: 'jav',\n  JPN: 'jpn',\n  KAN: 'kan',\n  KAT: 'kat',\n  KAT_OLD: 'kat_old',\n  KAZ: 'kaz',\n  KHM: 'khm',\n  KIR: 'kir',\n  KOR: 'kor',\n  KUR: 'kur',\n  LAO: 'lao',\n  LAT: 'lat',\n  LAV: 'lav',\n  LIT: 'lit',\n  MAL: 'mal',\n  MAR: 'mar',\n  MKD: 'mkd',\n  MLT: 'mlt',\n  MSA: 'msa',\n  MYA: 'mya',\n  NEP: 'nep',\n  NLD: 'nld',\n  NOR: 'nor',\n  ORI: 'ori',\n  PAN: 'pan',\n  POL: 'pol',\n  POR: 'por',\n  PUS: 'pus',\n  RON: 'ron',\n  RUS: 'rus',\n  SAN: 'san',\n  SIN: 'sin',\n  SLK: 'slk',\n  SLV: 'slv',\n  SPA: 'spa',\n  SPA_OLD: 'spa_old',\n  SQI: 'sqi',\n  SRP: 'srp',\n  SRP_LATN: 'srp_latn',\n  SWA: 'swa',\n  SWE: 'swe',\n  SYR: 'syr',\n  TAM: 'tam',\n  TEL: 'tel',\n  TGK: 'tgk',\n  TGL: 'tgl',\n  THA: 'tha',\n  TIR: 'tir',\n  TUR: 'tur',\n  UIG: 'uig',\n  UKR: 'ukr',\n  URD: 'urd',\n  UZB: 'uzb',\n  UZB_CYRL: 'uzb_cyrl',\n  VIE: 'vie',\n  YID: 'yid',\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;CAGC,GAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwGC,GAED;;EAEE,GACF,OAAO,OAAO,GAAG;IACf,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,UAAU;IACV,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,SAAS;IACT,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,SAAS;IACT,KAAK;IACL,KAAK;IACL,UAAU;IACV,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,UAAU;IACV,KAAK;IACL,KAAK;AACP", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3246, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/constants/PSM.js"], "sourcesContent": ["'use strict';\n\n/*\n * PSM = Page Segmentation Mode\n */\nmodule.exports = {\n  OSD_ONLY: '0',\n  AUTO_OSD: '1',\n  AUTO_ONLY: '2',\n  AUTO: '3',\n  SINGLE_COLUMN: '4',\n  SINGLE_BLOCK_VERT_TEXT: '5',\n  SINGLE_BLOCK: '6',\n  SINGLE_LINE: '7',\n  SINGLE_WORD: '8',\n  CIRCLE_WORD: '9',\n  SINGLE_CHAR: '10',\n  SPARSE_TEXT: '11',\n  SPARSE_TEXT_OSD: '12',\n  RAW_LINE: '13',\n};\n"], "names": [], "mappings": "AAAA;AAEA;;CAEC,GACD,OAAO,OAAO,GAAG;IACf,UAAU;IACV,UAAU;IACV,WAAW;IACX,MAAM;IACN,eAAe;IACf,wBAAwB;IACxB,cAAc;IACd,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,iBAAiB;IACjB,UAAU;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3270, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/node_modules/tesseract.js/src/index.js"], "sourcesContent": ["'use strict';\n\n/**\n *\n * Entry point for tesseract.js, should be the entry when bundling.\n *\n * @fileoverview entry point for tesseract.js\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <<EMAIL>>\n * <AUTHOR> <jero<PERSON><PERSON><EMAIL>>\n */\nrequire('regenerator-runtime/runtime');\nconst createScheduler = require('./createScheduler');\nconst createWorker = require('./createWorker');\nconst Tesseract = require('./Tesseract');\nconst languages = require('./constants/languages');\nconst OEM = require('./constants/OEM');\nconst PSM = require('./constants/PSM');\nconst { setLogging } = require('./utils/log');\n\nmodule.exports = {\n  languages,\n  OEM,\n  PSM,\n  createScheduler,\n  createWorker,\n  setLogging,\n  ...Tesseract,\n};\n"], "names": [], "mappings": "AAAA;AAEA;;;;;;;;CAQC;AAED,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM;AACN,MAAM,EAAE,UAAU,EAAE;AAEpB,OAAO,OAAO,GAAG;IACf;IACA;IACA;IACA;IACA;IACA;IACA,GAAG,SAAS;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3301, "column": 0}, "map": {"version": 3, "file": "FileSaver.min.js", "sources": ["file:///D:/AI/ex/delete-word/node_modules/file-saver/src/FileSaver.js"], "sourcesContent": ["/*\n* FileSaver.js\n* A saveAs() FileSaver implementation.\n*\n* By <PERSON>, http://eligrey.com\n*\n* License : https://github.com/eligrey/FileSaver.js/blob/master/LICENSE.md (MIT)\n* source  : http://purl.eligrey.com/github/FileSaver.js\n*/\n\n// The one and only way of getting global scope in all environments\n// https://stackoverflow.com/q/3277182/1008999\nvar _global = typeof window === 'object' && window.window === window\n  ? window : typeof self === 'object' && self.self === self\n  ? self : typeof global === 'object' && global.global === global\n  ? global\n  : this\n\nfunction bom (blob, opts) {\n  if (typeof opts === 'undefined') opts = { autoBom: false }\n  else if (typeof opts !== 'object') {\n    console.warn('Deprecated: Expected third argument to be a object')\n    opts = { autoBom: !opts }\n  }\n\n  // prepend BOM for UTF-8 XML and text/* types (including HTML)\n  // note: your browser will automatically convert UTF-16 U+FEFF to EF BB BF\n  if (opts.autoBom && /^\\s*(?:text\\/\\S*|application\\/xml|\\S*\\/\\S*\\+xml)\\s*;.*charset\\s*=\\s*utf-8/i.test(blob.type)) {\n    return new Blob([String.fromCharCode(0xFEFF), blob], { type: blob.type })\n  }\n  return blob\n}\n\nfunction download (url, name, opts) {\n  var xhr = new XMLHttpRequest()\n  xhr.open('GET', url)\n  xhr.responseType = 'blob'\n  xhr.onload = function () {\n    saveAs(xhr.response, name, opts)\n  }\n  xhr.onerror = function () {\n    console.error('could not download file')\n  }\n  xhr.send()\n}\n\nfunction corsEnabled (url) {\n  var xhr = new XMLHttpRequest()\n  // use sync to avoid popup blocker\n  xhr.open('HEAD', url, false)\n  try {\n    xhr.send()\n  } catch (e) {}\n  return xhr.status >= 200 && xhr.status <= 299\n}\n\n// `a.click()` doesn't work for all browsers (#465)\nfunction click (node) {\n  try {\n    node.dispatchEvent(new MouseEvent('click'))\n  } catch (e) {\n    var evt = document.createEvent('MouseEvents')\n    evt.initMouseEvent('click', true, true, window, 0, 0, 0, 80,\n                          20, false, false, false, false, 0, null)\n    node.dispatchEvent(evt)\n  }\n}\n\n// Detect WebView inside a native macOS app by ruling out all browsers\n// We just need to check for 'Safari' because all other browsers (besides Firefox) include that too\n// https://www.whatismybrowser.com/guides/the-latest-user-agent/macos\nvar isMacOSWebView = _global.navigator && /Macintosh/.test(navigator.userAgent) && /AppleWebKit/.test(navigator.userAgent) && !/Safari/.test(navigator.userAgent)\n\nvar saveAs = _global.saveAs || (\n  // probably in some web worker\n  (typeof window !== 'object' || window !== _global)\n    ? function saveAs () { /* noop */ }\n\n  // Use download attribute first if possible (#193 Lumia mobile) unless this is a macOS WebView\n  : ('download' in HTMLAnchorElement.prototype && !isMacOSWebView)\n  ? function saveAs (blob, name, opts) {\n    var URL = _global.URL || _global.webkitURL\n    var a = document.createElement('a')\n    name = name || blob.name || 'download'\n\n    a.download = name\n    a.rel = 'noopener' // tabnabbing\n\n    // TODO: detect chrome extensions & packaged apps\n    // a.target = '_blank'\n\n    if (typeof blob === 'string') {\n      // Support regular links\n      a.href = blob\n      if (a.origin !== location.origin) {\n        corsEnabled(a.href)\n          ? download(blob, name, opts)\n          : click(a, a.target = '_blank')\n      } else {\n        click(a)\n      }\n    } else {\n      // Support blobs\n      a.href = URL.createObjectURL(blob)\n      setTimeout(function () { URL.revokeObjectURL(a.href) }, 4E4) // 40s\n      setTimeout(function () { click(a) }, 0)\n    }\n  }\n\n  // Use msSaveOrOpenBlob as a second approach\n  : 'msSaveOrOpenBlob' in navigator\n  ? function saveAs (blob, name, opts) {\n    name = name || blob.name || 'download'\n\n    if (typeof blob === 'string') {\n      if (corsEnabled(blob)) {\n        download(blob, name, opts)\n      } else {\n        var a = document.createElement('a')\n        a.href = blob\n        a.target = '_blank'\n        setTimeout(function () { click(a) })\n      }\n    } else {\n      navigator.msSaveOrOpenBlob(bom(blob, opts), name)\n    }\n  }\n\n  // Fallback to using FileReader and a popup\n  : function saveAs (blob, name, opts, popup) {\n    // Open a popup immediately do go around popup blocker\n    // Mostly only available on user interaction and the fileReader is async so...\n    popup = popup || open('', '_blank')\n    if (popup) {\n      popup.document.title =\n      popup.document.body.innerText = 'downloading...'\n    }\n\n    if (typeof blob === 'string') return download(blob, name, opts)\n\n    var force = blob.type === 'application/octet-stream'\n    var isSafari = /constructor/i.test(_global.HTMLElement) || _global.safari\n    var isChromeIOS = /CriOS\\/[\\d]+/.test(navigator.userAgent)\n\n    if ((isChromeIOS || (force && isSafari) || isMacOSWebView) && typeof FileReader !== 'undefined') {\n      // Safari doesn't allow downloading of blob URLs\n      var reader = new FileReader()\n      reader.onloadend = function () {\n        var url = reader.result\n        url = isChromeIOS ? url : url.replace(/^data:[^;]*;/, 'data:attachment/file;')\n        if (popup) popup.location.href = url\n        else location = url\n        popup = null // reverse-tabnabbing #460\n      }\n      reader.readAsDataURL(blob)\n    } else {\n      var URL = _global.URL || _global.webkitURL\n      var url = URL.createObjectURL(blob)\n      if (popup) popup.location = url\n      else location.href = url\n      popup = null // reverse-tabnabbing #460\n      setTimeout(function () { URL.revokeObjectURL(url) }, 4E4) // 40s\n    }\n  }\n)\n\n_global.saveAs = saveAs.saveAs = saveAs\n\nif (typeof module !== 'undefined') {\n  module.exports = saveAs;\n}\n"], "names": [], "mappings": ";;;;;;;;IAkBA,QAAS,CAAA,CAAT,CAAc,CAAd,EAAoB,CAApB,CAA0B;QAAA,OACJ,WAAhB,IAAA,MAAO,CAAA,CADa,GACS,CAAI,GAAG;YAAE,OAAO,EAAA,CAAA;QAAT,CADhB,GAEC,QAAhB,IAAA,MAAO,CAAA,CAFQ,IAAA,CAGtB,OAAO,CAAC,IAAR,CAAa,oDAAb,CAHsB,EAItB,CAAI,GAAG;YAAE,OAAO,EAAE,CAAC;QAAZ,CAJe,GASpB,CAAI,CAAC,OAAL,IAAgB,6EAA6E,IAA7E,CAAkF,CAAI,CAAC,IAAvF,CATI,GAUf,GAAI,CAAA,IAAJ,CAAS;YAAA;YAA8B,CAA9B;SAAT,EAA8C;YAAE,IAAI,EAAE,CAAI,CAAC;QAAb,CAA9C,CAVe,GAYjB;IACR;IAED,QAAS,CAAA,CAAT,CAAmB,CAAnB,EAAwB,CAAxB,EAA8B,CAA9B,CAAoC;QAClC,GAAI,CAAA,CAAG,GAAG,GAAI,CAAA,cAAd;QACA,CAAG,CAAC,IAAJ,CAAS,KAAT,EAAgB,CAAhB,CAFkC,EAGlC,CAAG,CAAC,YAAJ,GAAmB,MAHe,EAIlC,CAAG,CAAC,MAAJ,GAAa,UAAY;YACvB,CAAM,CAAC,CAAG,CAAC,QAAL,EAAe,CAAf,EAAqB,CAArB;QACP,CANiC,EAOlC,CAAG,CAAC,OAAJ,GAAc,UAAY;YACxB,OAAO,CAAC,KAAR,CAAc,yBAAd;QACD,CATiC,EAUlC,CAAG,CAAC,IAAJ;IACD;IAED,QAAS,CAAA,CAAT,CAAsB,CAAtB,CAA2B;QACzB,GAAI,CAAA,CAAG,GAAG,GAAI,CAAA,cAAd;QAEA,CAAG,CAAC,IAAJ,CAAS,MAAT,EAAiB,CAAjB,EAAA,CAAA,EAHyB;QAIzB,IAAI;YACF,CAAG,CAAC,IAAJ;QACD,EAAC,OAAO,CAAP,EAAU,CAAE;QACd,MAAqB,CAAA,GAAd,IAAA,CAAG,CAAC,MAAJ,IAAmC,GAAd,IAAA,CAAG,CAAC;IACjC;IAGD,QAAS,CAAA,CAAT,CAAgB,CAAhB,CAAsB;QACpB,IAAI;YACF,CAAI,CAAC,aAAL,CAAmB,GAAI,CAAA,UAAJ,CAAe,OAAf,CAAnB;QACD,EAAC,OAAO,CAAP,EAAU;YACV,GAAI,CAAA,CAAG,GAAG,QAAQ,CAAC,WAAT,CAAqB,aAArB,CAAV;YACA,CAAG,CAAC,cAAJ,CAAmB,OAAnB,EAAA,CAAA,GAAA,CAAA,GAAwC,MAAxC,EAAgD,CAAhD,EAAmD,CAAnD,EAAsD,CAAtD,EAAyD,EAAzD,EACsB,EADtB,EAAA,CAAA,GAAA,CAAA,GAAA,CAAA,GAAA,CAAA,GACsD,CADtD,EACyD,IADzD,CAFU,EAIV,CAAI,CAAC,aAAL,CAAmB,CAAnB;QACD;IACF;I,GAtDG,CAAA,CAAO,GAAqB,QAAlB,IAAA,MAAO,CAAA,MAAP,IAA8B,MAAM,CAAC,MAAP,KAAkB,MAAhD,GACV,MADU,GACe,QAAhB,IAAA,MAAO,CAAA,IAAP,IAA4B,IAAI,CAAC,IAAL,KAAc,IAA1C,GACT,IADS,GACgB,QAAlB,IAAA,MAAO,CAAA,MAAP,IAA8B,MAAM,CAAC,MAAP,KAAkB,MAAhD,GACP,MADO,GAAA,KAAA,C,EAyDP,CAAc,GAAG,CAAO,CAAC,SAAR,IAAqB,YAAY,IAAZ,CAAiB,SAAS,CAAC,SAA3B,CAArB,IAA8D,cAAc,IAAd,CAAmB,SAAS,CAAC,SAA7B,CAA9D,IAAyG,CAAC,SAAS,IAAT,CAAc,SAAS,CAAC,SAAxB,C,EAE3H,CAAM,GAAG,CAAO,CAAC,MAAR,IAAA,CAEQ,QAAlB,IAAA,MAAO,CAAA,MAAP,IAA8B,MAAM,KAAK,CAA1C,GACI,UAAmB,EAAc,CADrC,GAIG,YAAc,EAAA,iBAAiB,CAAC,SAAhC,IAA6C,CAAC,CAA/C,GACA,SAAiB,CAAjB,EAAuB,CAAvB,EAA6B,CAA7B,CAAmC;QAAA,GAC/B,CAAA,CAAG,GAAG,CAAO,CAAC,GAAR,IAAe,CAAO,CAAC,SADE,EAE/B,CAAC,GAAG,QAAQ,CAAC,aAAT,CAAuB,GAAvB,CAF2B;QAGnC,CAAI,GAAG,CAAI,IAAI,CAAI,CAAC,IAAb,IAAqB,UAHO,EAKnC,CAAC,CAAC,QAAF,GAAa,CALsB,EAMnC,CAAC,CAAC,GAAF,GAAQ,UAN2B,EAWf,QAAhB,IAAA,MAAO,CAAA,CAXwB,GAAA,CAajC,CAAC,CAAC,IAAF,GAAS,CAbwB,EAc7B,CAAC,CAAC,MAAF,KAAa,QAAQ,CAAC,MAdO,GAmB/B,CAAK,CAAC,CAAD,CAnB0B,GAe/B,CAAW,CAAC,CAAC,CAAC,IAAH,CAAX,GACI,CAAQ,CAAC,CAAD,EAAO,CAAP,EAAa,CAAb,CADZ,GAEI,CAAK,CAAC,CAAD,EAAI,CAAC,CAAC,MAAF,GAAW,QAAf,CAjBsB,IAAA,CAuBjC,CAAC,CAAC,IAAF,GAAS,CAAG,CAAC,eAAJ,CAAoB,CAApB,CAvBwB,EAwBjC,UAAU,CAAC,UAAY;YAAE,CAAG,CAAC,eAAJ,CAAoB,CAAC,CAAC,IAAtB;QAA6B,CAA5C,EAA8C,GAA9C,CAxBuB,EAyBjC,UAAU,CAAC,UAAY;YAAE,CAAK,CAAC,CAAD;QAAK,CAAzB,EAA2B,CAA3B,CAzBuB;IA2BpC,CA5BC,GA+BA,oBAAsB,EAAA,SAAtB,GACA,SAAiB,CAAjB,EAAuB,CAAvB,EAA6B,CAA7B,CAAmC;QAGnC,IAFA,CAAI,GAAG,CAAI,IAAI,CAAI,CAAC,IAAb,IAAqB,UAE5B,EAAoB,QAAhB,IAAA,MAAO,CAAA,CAAX,EAUE,SAAS,CAAC,gBAAV,CAA2B,CAAG,CAAC,CAAD,EAAO,CAAP,CAA9B,EAA4C,CAA5C,CAVF,KACE;aAAA,IAAI,CAAW,CAAC,CAAD,CAAf,EACE,CAAQ,CAAC,CAAD,EAAO,CAAP,EAAa,CAAb,CADV;aAEO;YACL,GAAI,CAAA,CAAC,GAAG,QAAQ,CAAC,aAAT,CAAuB,GAAvB,CAAR;YACA,CAAC,CAAC,IAAF,GAAS,CAFJ,EAGL,CAAC,CAAC,MAAF,GAAW,QAHN,EAIL,UAAU,CAAC,UAAY;gBAAE,CAAK,CAAC,CAAD;YAAK,CAAzB;QACX;IAIJ,CAhBC,GAmBA,SAAiB,CAAjB,EAAuB,CAAvB,EAA6B,CAA7B,EAAmC,CAAnC,CAA0C;QAS1C,IANA,CAAK,GAAG,CAAK,IAAI,IAAI,CAAC,EAAD,EAAK,QAAL,CAMrB,EALI,CAKJ,IAAA,CAJE,CAAK,CAAC,QAAN,CAAe,KAAf,GACA,CAAK,CAAC,QAAN,CAAe,IAAf,CAAoB,SAApB,GAAgC,gBAGlC,GAAoB,QAAhB,IAAA,MAAO,CAAA,CAAX,EAA8B,MAAO,CAAA,CAAQ,CAAC,CAAD,EAAO,CAAP,EAAa,CAAb,CAAf;QATY,GAWtC,CAAA,CAAK,GAAiB,0BAAd,KAAA,CAAI,CAAC,IAXyB,EAYtC,CAAQ,GAAG,eAAe,IAAf,CAAoB,CAAO,CAAC,WAA5B,KAA4C,CAAO,CAAC,MAZzB,EAatC,CAAW,GAAG,eAAe,IAAf,CAAoB,SAAS,CAAC,SAA9B,CAbwB;QAe1C,IAAI,CAAC,CAAW,IAAK,CAAK,IAAI,CAAzB,IAAsC,CAAvC,KAAgF,WAAtB,IAAA,MAAO,CAAA,UAArE,EAAiG;YAE/F,GAAI,CAAA,CAAM,GAAG,GAAI,CAAA,UAAjB;YACA,CAAM,CAAC,SAAP,GAAmB,UAAY;gBAC7B,GAAI,CAAA,CAAG,GAAG,CAAM,CAAC,MAAjB;gBACA,CAAG,GAAG,CAAW,GAAG,CAAH,GAAS,CAAG,CAAC,OAAJ,CAAY,cAAZ,EAA4B,uBAA5B,CAFG,EAGzB,CAHyB,GAGlB,CAAK,CAAC,QAAN,CAAe,IAAf,GAAsB,CAHJ,GAIxB,QAAQ,GAAG,CAJa,EAK7B,CAAK,GAAG;YACT,CAT8F,EAU/F,CAAM,CAAC,aAAP,CAAqB,CAArB;QACD,CAXD,MAWO;YAAA,GACD,CAAA,CAAG,GAAG,CAAO,CAAC,GAAR,IAAe,CAAO,CAAC,SAD5B,EAED,CAAG,GAAG,CAAG,CAAC,eAAJ,CAAoB,CAApB,CAFL;YAGD,CAHC,GAGM,CAAK,CAAC,QAAN,GAAiB,CAHvB,GAIA,QAAQ,CAAC,IAAT,GAAgB,CAJhB,EAKL,CAAK,GAAG,IALH,EAML,UAAU,CAAC,UAAY;gBAAE,CAAG,CAAC,eAAJ,CAAoB,CAApB;YAA0B,CAAzC,EAA2C,GAA3C;QACX;IACF,CA1FU,C;IA6Fb,CAAO,CAAC,MAAR,GAAiB,CAAM,CAAC,MAAP,GAAgB,C,EAEX,WAAlB,QAAO,CAAA,M,mC,CACT,MAAM,CAAC,OAAP,GAAiB,C;A", "ignoreList": [0], "debugId": null}}]}