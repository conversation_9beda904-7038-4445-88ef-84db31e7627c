export interface OCRResult {
  text: string;
  confidence: number;
  words: Array<{
    text: string;
    confidence: number;
    bbox: {
      x0: number;
      y0: number;
      x1: number;
      y1: number;
    };
  }>;
}

export interface TextMatch {
  text: string;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  confidence: number;
}

class BaiduOCRService {
  async recognizeText(imageFile: File): Promise<OCRResult> {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      // 首先尝试真实的百度OCR API
      let response = await fetch('/api/ocr', {
        method: 'POST',
        body: formData,
      });

      // 如果百度OCR API失败（可能是未配置），使用测试API
      if (!response.ok) {
        console.warn('Baidu OCR API not available, using test OCR...');
        response = await fetch('/api/test-ocr', {
          method: 'POST',
          body: formData,
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'OCR request failed');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('OCR recognition failed:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to recognize text in image');
    }
  }

  findTextMatches(ocrResult: OCRResult, targetTexts: string[]): TextMatch[] {
    const matches: TextMatch[] = [];

    // 对目标文字进行预处理，保持原始大小写用于中文，小写用于英文
    const processedTargets = targetTexts.map(text => ({
      original: text.trim(),
      normalized: text.toLowerCase().trim(),
      isChinese: /[\u4e00-\u9fff]/.test(text)
    }));

    console.log('=== 开始文字匹配过程 ===');
    console.log('目标关键词数量:', processedTargets.length);
    console.log('目标关键词:', processedTargets.map(t => t.original));
    console.log('OCR识别词数量:', ocrResult.words.length);
    console.log('OCR识别的词:', ocrResult.words.map(w => w.text));
    console.log('完整OCR文本:', ocrResult.text);

    // 检查每个OCR识别的词
    ocrResult.words.forEach(word => {
      const originalWord = word.text.trim();
      const normalizedWord = word.text.toLowerCase().trim();

      console.log(`\n--- 检查OCR词: "${originalWord}" ---`);

      processedTargets.forEach((target, targetIndex) => {
        console.log(`  检查目标词 ${targetIndex + 1}/${processedTargets.length}: "${target.original}" (中文: ${target.isChinese})`);
        let isMatch = false;
        let matchType = '';

        // 1. 精确匹配（中文保持原样，英文忽略大小写）
        if (target.isChinese) {
          if (originalWord === target.original || originalWord.includes(target.original) || target.original.includes(originalWord)) {
            isMatch = true;
            matchType = '中文精确/包含匹配';
          }
        } else {
          if (normalizedWord === target.normalized || normalizedWord.includes(target.normalized) || target.normalized.includes(normalizedWord)) {
            isMatch = true;
            matchType = '英文精确/包含匹配';
          }
        }

        // 2. 模糊匹配 - 处理OCR识别错误
        if (!isMatch && this.fuzzyMatch(originalWord, target.original)) {
          isMatch = true;
          matchType = '模糊匹配';
        }

        // 3. 字符级别匹配 - 对于中文特别有用
        if (!isMatch && target.isChinese && originalWord.length > 0) {
          // 检查是否包含目标中文字符
          const targetChars = target.original.split('');
          const wordChars = originalWord.split('');
          const commonChars = targetChars.filter(char => wordChars.includes(char));

          if (commonChars.length >= Math.min(targetChars.length, wordChars.length) * 0.7) {
            isMatch = true;
            matchType = '中文字符匹配';
          }
        }

        if (isMatch) {
          console.log(`    ✅ 匹配成功: "${originalWord}" <-> "${target.original}" (${matchType})`);
          matches.push({
            text: word.text,
            bbox: word.bbox,
            confidence: matchType === '模糊匹配' ? word.confidence * 0.8 : word.confidence
          });
        } else {
          console.log(`    ❌ 不匹配: "${originalWord}" <-> "${target.original}"`);
        }
      });
    });

    // 去重
    const uniqueMatches = matches.filter((match, index, self) =>
      index === self.findIndex(m =>
        m.bbox.x0 === match.bbox.x0 &&
        m.bbox.y0 === match.bbox.y0 &&
        m.text === match.text
      )
    );

    console.log('\n=== 匹配结果总结 ===');
    console.log(`总共找到 ${uniqueMatches.length} 个匹配:`);
    uniqueMatches.forEach((match, index) => {
      console.log(`  ${index + 1}. "${match.text}" (置信度: ${match.confidence.toFixed(0)}%)`);
    });
    console.log('=== 匹配过程结束 ===\n');

    return uniqueMatches;
  }

  // 模糊匹配算法 - 处理OCR可能的识别错误
  private fuzzyMatch(word: string, target: string): boolean {
    if (word.length < 2 || target.length < 2) return false;
    
    // 计算编辑距离
    const editDistance = this.levenshteinDistance(word, target);
    const maxLength = Math.max(word.length, target.length);
    
    // 如果编辑距离小于长度的30%，认为是匹配的
    return editDistance / maxLength < 0.3;
  }

  // 计算编辑距离
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // 替换
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j] + 1      // 删除
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
}

// 导出单例实例
export const baiduOcrService = new BaiduOCRService();

// 读取文本文件内容的工具函数
export async function readTextFile(file: File): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const content = event.target?.result as string;
      const lines = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);
      resolve(lines);
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read text file'));
    };
    
    reader.readAsText(file);
  });
}
