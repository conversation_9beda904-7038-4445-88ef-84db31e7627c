export interface OCRResult {
  text: string;
  confidence: number;
  words: Array<{
    text: string;
    confidence: number;
    bbox: {
      x0: number;
      y0: number;
      x1: number;
      y1: number;
    };
  }>;
}

export interface TextMatch {
  text: string;
  bbox: {
    x0: number;
    y0: number;
    x1: number;
    y1: number;
  };
  confidence: number;
}

class BaiduOCRService {
  async recognizeText(imageFile: File): Promise<OCRResult> {
    try {
      const formData = new FormData();
      formData.append('image', imageFile);

      // 首先尝试真实的百度OCR API
      let response = await fetch('/api/ocr', {
        method: 'POST',
        body: formData,
      });

      // 如果百度OCR API失败（可能是未配置），使用测试API
      if (!response.ok) {
        console.warn('Baidu OCR API not available, using test OCR...');
        response = await fetch('/api/test-ocr', {
          method: 'POST',
          body: formData,
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'OCR request failed');
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('OCR recognition failed:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to recognize text in image');
    }
  }

  findTextMatches(ocrResult: OCRResult, targetTexts: string[]): TextMatch[] {
    const matches: TextMatch[] = [];
    const normalizedTargets = targetTexts.map(text => text.toLowerCase().trim());

    // 检查每个OCR识别的词
    ocrResult.words.forEach(word => {
      const normalizedWord = word.text.toLowerCase().trim();
      
      // 直接匹配
      if (normalizedTargets.includes(normalizedWord)) {
        matches.push({
          text: word.text,
          bbox: word.bbox,
          confidence: word.confidence
        });
        return;
      }
      
      // 部分匹配 - 检查是否包含目标文字
      normalizedTargets.forEach(target => {
        if (normalizedWord.includes(target) || target.includes(normalizedWord)) {
          matches.push({
            text: word.text,
            bbox: word.bbox,
            confidence: word.confidence
          });
        }
      });
      
      // 模糊匹配 - 处理可能的OCR识别错误
      normalizedTargets.forEach(target => {
        if (this.fuzzyMatch(normalizedWord, target)) {
          matches.push({
            text: word.text,
            bbox: word.bbox,
            confidence: word.confidence * 0.8 // 降低模糊匹配的置信度
          });
        }
      });
    });

    // 去重
    const uniqueMatches = matches.filter((match, index, self) => 
      index === self.findIndex(m => 
        m.bbox.x0 === match.bbox.x0 && 
        m.bbox.y0 === match.bbox.y0 &&
        m.text === match.text
      )
    );

    return uniqueMatches;
  }

  // 模糊匹配算法 - 处理OCR可能的识别错误
  private fuzzyMatch(word: string, target: string): boolean {
    if (word.length < 2 || target.length < 2) return false;
    
    // 计算编辑距离
    const editDistance = this.levenshteinDistance(word, target);
    const maxLength = Math.max(word.length, target.length);
    
    // 如果编辑距离小于长度的30%，认为是匹配的
    return editDistance / maxLength < 0.3;
  }

  // 计算编辑距离
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // 替换
            matrix[i][j - 1] + 1,     // 插入
            matrix[i - 1][j] + 1      // 删除
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }
}

// 导出单例实例
export const baiduOcrService = new BaiduOCRService();

// 读取文本文件内容的工具函数
export async function readTextFile(file: File): Promise<string[]> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      const content = event.target?.result as string;
      const lines = content
        .split('\n')
        .map(line => line.trim())
        .filter(line => line.length > 0);
      resolve(lines);
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read text file'));
    };
    
    reader.readAsText(file);
  });
}
