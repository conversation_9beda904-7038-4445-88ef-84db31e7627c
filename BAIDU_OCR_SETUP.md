# 百度OCR配置说明

## 1. 获取百度OCR API密钥

### 步骤1: 注册百度智能云账号
1. 访问 [百度智能云](https://cloud.baidu.com/)
2. 注册并登录账号

### 步骤2: 开通文字识别服务
1. 进入 [文字识别控制台](https://console.bce.baidu.com/ai/#/ai/ocr/overview/index)
2. 点击"立即使用"开通服务
3. 选择合适的套餐（有免费额度）

### 步骤3: 创建应用
1. 在控制台点击"创建应用"
2. 填写应用名称和描述
3. 选择需要的接口（通用文字识别）
4. 创建完成后获取以下信息：
   - AppID
   - API Key
   - Secret Key

## 2. 配置环境变量

### 方法1: 修改 .env.local 文件
```bash
# 编辑项目根目录下的 .env.local 文件
BAIDU_APP_ID=你的AppID
BAIDU_API_KEY=你的API_Key
BAIDU_SECRET_KEY=你的Secret_Key
```

### 方法2: 系统环境变量
在系统中设置以下环境变量：
- `BAIDU_APP_ID`
- `BAIDU_API_KEY`
- `BAIDU_SECRET_KEY`

## 3. 重启应用
配置完成后，重启开发服务器：
```bash
npm run dev
```

## 4. 测试OCR功能
1. 上传包含文字的图片
2. 上传文本文件（包含要移除的关键词）
3. 点击"Process Image"开始处理

## 5. 费用说明
- 百度OCR提供免费额度
- 通用文字识别：每月1000次免费调用
- 超出免费额度后按调用次数收费
- 详细价格请查看 [百度OCR价格页面](https://cloud.baidu.com/product/ocr/price.html)

## 6. API限制
- 图片大小：最大4MB
- 支持格式：JPG, JPEG, PNG, BMP
- 图片最小尺寸：15x15像素
- 图片最大尺寸：4096x4096像素

## 7. 优势
相比Tesseract.js，百度OCR具有以下优势：
- ✅ 识别精度更高
- ✅ 对中文支持更好
- ✅ 处理速度更快
- ✅ 支持更多字体和场景
- ✅ 自动纠错能力强

## 8. 故障排除

### 常见错误及解决方案

**错误: "Baidu OCR credentials not configured"**
- 检查环境变量是否正确设置
- 确认 .env.local 文件在项目根目录
- 重启开发服务器

**错误: "Failed to get access token"**
- 检查API Key和Secret Key是否正确
- 确认百度智能云账号状态正常
- 检查网络连接

**错误: "Image file too large"**
- 压缩图片到4MB以下
- 使用支持的图片格式

**错误: "Baidu OCR API error"**
- 检查账号余额和调用次数
- 确认服务状态正常
- 查看百度云控制台的错误日志

## 9. 联系支持
如果遇到问题，可以：
- 查看百度智能云文档
- 联系百度技术支持
- 在项目中提交Issue
