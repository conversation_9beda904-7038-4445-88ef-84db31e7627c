{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/AI/ex/delete-word/src/app/api/test-ocr/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\n// 测试OCR API - 返回模拟数据\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const imageFile = formData.get('image') as File;\n    \n    if (!imageFile) {\n      return NextResponse.json(\n        { error: 'No image file provided' },\n        { status: 400 }\n      );\n    }\n\n    // 验证文件类型\n    if (!imageFile.type.startsWith('image/')) {\n      return NextResponse.json(\n        { error: 'Invalid file type. Please upload an image.' },\n        { status: 400 }\n      );\n    }\n\n    // 模拟OCR结果 - 返回一些常见的测试文字\n    const mockResult = {\n      text: 'SAMPLE TEXT WATERMARK COPYRIGHT CONFIDENTIAL 测试 水印 版权',\n      confidence: 95,\n      words: [\n        {\n          text: 'SAMPLE',\n          confidence: 98,\n          bbox: { x0: 100, y0: 50, x1: 180, y1: 80 }\n        },\n        {\n          text: 'TEXT',\n          confidence: 97,\n          bbox: { x0: 200, y0: 50, x1: 250, y1: 80 }\n        },\n        {\n          text: 'WATERMARK',\n          confidence: 96,\n          bbox: { x0: 300, y0: 100, x1: 420, y1: 130 }\n        },\n        {\n          text: 'COPYRIGHT',\n          confidence: 95,\n          bbox: { x0: 100, y0: 150, x1: 220, y1: 180 }\n        },\n        {\n          text: 'CONFIDENTIAL',\n          confidence: 94,\n          bbox: { x0: 250, y0: 150, x1: 380, y1: 180 }\n        },\n        {\n          text: '测试',\n          confidence: 93,\n          bbox: { x0: 100, y0: 200, x1: 140, y1: 230 }\n        },\n        {\n          text: '水印',\n          confidence: 92,\n          bbox: { x0: 160, y0: 200, x1: 200, y1: 230 }\n        },\n        {\n          text: '版权',\n          confidence: 91,\n          bbox: { x0: 220, y0: 200, x1: 260, y1: 230 }\n        }\n      ]\n    };\n\n    // 添加延迟模拟真实API调用\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    return NextResponse.json(mockResult);\n\n  } catch (error) {\n    console.error('Test OCR processing error:', error);\n    return NextResponse.json(\n      { error: 'Test OCR processing failed' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,YAAY,SAAS,GAAG,CAAC;QAE/B,IAAI,CAAC,WAAW;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,WAAW;YACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA6C,GACtD;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,aAAa;YACjB,MAAM;YACN,YAAY;YACZ,OAAO;gBACL;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAI,IAAI;wBAAK,IAAI;oBAAG;gBAC3C;gBACA;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAI,IAAI;wBAAK,IAAI;oBAAG;gBAC3C;gBACA;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAK,IAAI;wBAAK,IAAI;oBAAI;gBAC7C;gBACA;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAK,IAAI;wBAAK,IAAI;oBAAI;gBAC7C;gBACA;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAK,IAAI;wBAAK,IAAI;oBAAI;gBAC7C;gBACA;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAK,IAAI;wBAAK,IAAI;oBAAI;gBAC7C;gBACA;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAK,IAAI;wBAAK,IAAI;oBAAI;gBAC7C;gBACA;oBACE,MAAM;oBACN,YAAY;oBACZ,MAAM;wBAAE,IAAI;wBAAK,IAAI;wBAAK,IAAI;wBAAK,IAAI;oBAAI;gBAC7C;aACD;QACH;QAEA,gBAAgB;QAChB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA6B,GACtC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}