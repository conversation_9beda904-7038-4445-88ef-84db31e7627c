{"name": "mocha", "version": "4.1.0", "description": "simple, flexible, fun test framework", "keywords": ["mocha", "test", "bdd", "tdd", "tap"], "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["aaroncrows (https://github.com/aaroncrows)", "<PERSON> <<EMAIL>> (https://github.com/ahamid)", "<PERSON> <<EMAIL>> (https://github.com/aheckmann)", "<PERSON> (<PERSON><PERSON><PERSON><PERSON>'s alias) (https://github.com/CrabBot)", "<PERSON> (https://github.com/adamgruber)", "<PERSON> (https://github.com/adrian-ludwig)", "Ainthe Kitchen <<EMAIL>> (https://github.com/ainthek)", "ajay<PERSON><PERSON><PERSON> (https://github.com/ajaykodali)", "<PERSON> (https://github.com/aearly)", "<PERSON> <<EMAIL>> (https://github.com/thedark1337)", "amsul (https://github.com/amsul)", "<PERSON> <<EMAIL>> (https://github.com/abrkn)", "<PERSON> <<EMAIL>> (https://github.com/papandreou)", "<PERSON> <<EMAIL>> (https://github.com/vnikiti)", "<PERSON> <<EMAIL>> (https://github.com/andrew)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/andreypopp)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/eagleeye)", "<PERSON><PERSON> (https://github.com/anis)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/arian)", "<PERSON> <<EMAIL>> (https://github.com/a8m)", "<PERSON><PERSON><PERSON> (https://github.com/A<PERSON>ud<PERSON>)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/atsuya)", "<PERSON><PERSON><PERSON> (https://github.com/adomokos)", "<PERSON> (https://github.com/austinbirch)", "<PERSON><PERSON> (https://github.com/AviVahl)", "<PERSON> (https://github.com/ben-bradley)", "beneidel (https://github.com/beneidel)", "<PERSON><PERSON><PERSON> (https://github.com/benjie)", "<PERSON> <<EMAIL>> (https://github.com/bnoordhuis)", "<PERSON><PERSON> (https://github.com/zetaben)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/Ben<PERSON>)", "<PERSON> (https://github.com/benvinegar)", "Berk<PERSON> Peksag <<EMAIL>> (https://github.com/berkerpeksag)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/bjoerge)", "<PERSON> <<EMAIL>> (https://github.com/brendannee)", "<PERSON> <<EMAIL>> (https://github.com/exogen)", "<PERSON> <<EMAIL>> (https://github.com/brianc)", "<PERSON> <<EMAIL>> (https://github.com/blalor)", "<PERSON> (https://github.com/bionicbrian)", "<PERSON> (https://github.com/<PERSON>)", "<PERSON> (https://github.com/backspace)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/cscott)", "<PERSON> (https://github.com/caseywebdev)", "<PERSON> <<EMAIL>> (https://github.com/cowboyd)", "<PERSON> <<EMAIL>> (https://github.com/cmbuckley)", "<PERSON> <<EMAIL>> (https://github.com/boneskull)", "<PERSON> <<EMAIL>> (https://github.com/<PERSON>)", "<PERSON><PERSON><PERSON> (https://github.com/klaemo)", "<PERSON> (https://github.com/Connorhd)", "<PERSON> (https://github.com/coreybutler)", "<PERSON> (https://github.com/dump247)", "cybertk (https://github.com/cybertk)", "<PERSON> (https://github.com/monowerker)", "<PERSON> <<EMAIL>> (https://github.com/danielstjules)", "<PERSON> <<EMAIL>> (https://github.com/evocateur)", "<PERSON> <<EMAIL>> (https://github.com/davemckenna01)", "<PERSON> <<EMAIL>> (https://github.com/dasilvacontin)", "<PERSON> (https://github.com/dhendo)", "<PERSON> <<EMAIL>> (https://github.com/btd)", "<PERSON> <<EMAIL>> (https://github.com/sukima)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/diogogmt)", "<PERSON> <<EMAIL>> (https://github.com/runk)", "Domenic Denicola <<EMAIL>> (https://github.com/domenic)", "<PERSON> <<EMAIL>> (https://github.com/dominicbarnes)", "domq (https://github.com/domq)", "<PERSON> <<EMAIL>> (https://github.com/dougwilson)", "<PERSON> <<EMAIL>> (https://github.com/duncanbeevers)", "<PERSON> (https://github.com/badunk)", "eiji.ienaga (https://github.com/haru01)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/piuccio)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/2fd)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/indutny)", "fengmk2 <<EMAIL>> (https://github.com/fengmk2)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/ralt)", "Forbes Lindesay (https://github.com/ForbesLindesay)", "<PERSON><PERSON> (https://github.com/fredericosilva)", "<PERSON><PERSON> Enestad <<EMAIL>> (https://github.com/fredr)", "<PERSON><PERSON> (https://github.com/Cowboy-coder)", "<PERSON> (https://github.com/gsilk)", "<PERSON> <<EMAIL>> (https://github.com/gaye)", "<PERSON> <<EMAIL>> (https://github.com/halkeye)", "gigadude (https://github.com/gigadude)", "<PERSON> (https://github.com/giggio)", "<PERSON> <<EMAIL>> (https://github.com/curvedmark)", "<PERSON> <<EMAIL>> (https://github.com/glenjamin)", "<PERSON> <greg<PERSON><EMAIL>> (https://github.com/gregrperkins)", "<PERSON> <<EMAIL>> (https://github.com/rauchg)", "<PERSON> (https://github.com/aryeguy)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/gyandeeps)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/hyeluri)", "<PERSON> <<EMAIL>> (https://github.com/airhorns)", "<PERSON> <<EMAIL>> (https://github.com/ianwremmel)", "<PERSON> (https://github.com/ianstormtaylor)", "<PERSON> <<EMAIL>> (https://github.com/iangreenleaf)", "<PERSON> (https://github.com/i<PERSON><PERSON><PERSON><PERSON>)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/jsalonen)", "<PERSON> <<EMAIL>> (https://github.com/wejendorp)", "<PERSON> (https://github.com/jakecraige)", "<PERSON> (https://github.com/jakemmarsh)", "<PERSON> <<EMAIL>> (https://github.com/startswithaj)", "<PERSON> <<EMAIL>> (https://github.com/Raynos)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/zzen)", "<PERSON> (https://github.com/jbowes)", "<PERSON> <<EMAIL>> (https://github.com/jamescarr)", "<PERSON> <<EMAIL>> (https://github.com/jgkim)", "<PERSON> <<EMAIL>> (https://github.com/lightsofapollo)", "<PERSON> <<EMAIL>> (https://github.com/nylen)", "<PERSON> (https://github.com/jlai)", "<PERSON> <<EMAIL>> (https://github.com/JCBarry)", "<PERSON> <<EMAIL>> (https://github.com/javierav)", "jcreamer898 (https://github.com/jcreamer898)", "<PERSON> (https://github.com/nopnop)", "<PERSON> (https://github.com/kunklejr)", "<PERSON> <<EMAIL>> (https://github.com/jschilli)", "<PERSON><PERSON><PERSON><PERSON> (aka Outsider) <<EMAIL>> (https://github.com/outsideris)", "<PERSON> (https://github.com/jmar777)", "jimenglish81 (https://github.com/jimenglish81)", "<PERSON> (https://github.com/jimmycu<PERSON>ra)", "jldailey (https://github.com/jldailey)", "jleyba (https://github.com/jleyba)", "<PERSON> <<EMAIL>> (https://github.com/joeycozza)", "<PERSON><PERSON><PERSON> (https://github.com/outdooricon)", "<PERSON> <<EMAIL>> (https://github.com/jrhdoty)", "<PERSON> <<EMAIL>> (https://github.com/jfirebaugh)", "<PERSON> (https://github.com/jonnyreeves)", "<PERSON> <<EMAIL>> (https://github.com/joliss)", "<PERSON> (https://github.com/dohse)", "<PERSON> <<EMAIL>> (https://github.com/jkimbo)", "<PERSON> <<EMAIL>> (https://github.com/park9140)", "jongleberry <<EMAIL>> (https://github.com/jonathanong)", "<PERSON> <<EMAIL>> (https://github.com/jordansexton)", "<PERSON> (https://github.com/jsdevel)", "<PERSON> (https://github.com/joshlory)", "<PERSON> <<EMAIL>> (https://github.com/jbnicolai)", "<PERSON> <<EMAIL>> (https://github.com/jkrall)", "<PERSON> (https://github.com/joaomoreno)", "<PERSON> <<EMAIL>> (https://github.com/jpbochi)", "jugglinmike (https://github.com/jugglinmike)", "<PERSON> (https://github.com/julienw)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/jvirtanen)", "<PERSON> (https://github.com/justindujardin)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/juzerali)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/astorije)", "<PERSON> (https://github.com/kategengler)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/hokaccha)", "<PERSON> (https://github.com/keithamus)", "<PERSON> <PERSON> <<EMAIL>> (https://github.com/kentcdodds)", "<PERSON> <<EMAIL>> (https://github.com/kevinburke)", "<PERSON> <<EMAIL>> (https://github.com/kevinconway)", "<PERSON> <Kev.<PERSON><EMAIL>> (https://github.com/kkirsche)", "<PERSON><PERSON> <kirill.k<PERSON><PERSON><PERSON>@gmail.com> (https://github.com/Dremora)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/koenpunt)", "<PERSON> <<EMAIL>> (https://github.com/kkaefer)", "<PERSON> (https://github.com/krisr)", "<PERSON> <<EMAIL>> (https://github.com/kemitchell)", "lakmeer (https://github.com/lakmeer)", "<PERSON> <<EMAIL>> (https://github.com/bitwiseman)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/LinusU)", "<PERSON> <<EMAIL>> (https://github.com/longlho)", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/lackac)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/mmalecki)", "<PERSON> (https://github.com/mal)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/marcello3d)", "<PERSON> <<EMAIL>> (https://github.com/mck-)", "<PERSON> (https://github.com/Standard8)", "<PERSON><PERSON> <matija.ma<PERSON><PERSON><PERSON>@gmail.com> (https://github.com/silvenon)", "<PERSON> <matthews<PERSON><PERSON>@littlesecretsrecords.com> (https://github.com/arkadyan)", "mattias-lw (https://github.com/mattias-lw)", "<PERSON> <<EMAIL>> (https://github.com/mattrobenolt)", "<PERSON> <<EMAIL>> (https://github.com/twobitfool)", "<PERSON> <<EMAIL>> (https://github.com/chromakode)", "<PERSON> <<EMAIL>> (https://github.com/mantoni)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/iam<PERSON>rick)", "michael-adsk (https://github.com/michael-adsk)", "<PERSON> (https://github.com/demmer)", "<PERSON> <<EMAIL>> (https://github.com/mjackson)", "<PERSON> <<EMAIL>> (https://github.com/Schoonology)", "<PERSON><PERSON> (https://github.com/michalc)", "<PERSON> (https://github.com/mwolson)", "<PERSON><PERSON> <mislav.ma<PERSON><PERSON><PERSON>@gmail.com> (https://github.com/mislav)", "mr<PERSON><PERSON><PERSON><PERSON> (https://github.com/mrShturman)", "<PERSON> <<EMAIL>> (https://github.com/nathanalderson)", "<PERSON> <<EMAIL>> (https://github.com/nathanboktae)", "<PERSON> <<EMAIL>> (https://github.com/nathanbowser)", "<PERSON> <<EMAIL>> (https://github.com/ndhoule)", "<PERSON> <<EMAIL>> (https://github.com/TooTallNate)", "<PERSON> (https://github.com/fitzgen)", "<PERSON><PERSON> (https://github.com/ngeor)", "noirlab (https://github.com/noirlab)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/noshir-patel)", "OlegTsyba <<EMAIL>> (https://github.com/OlegTsyba)", "omar (https://github.com/omardelarosa)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/panuhorsmal<PERSON>ti)", "<PERSON> <<EMAIL>> (https://github.com/parkr)", "<PERSON> (https://github.com/paularmstrong)", "<PERSON> <<EMAIL>> (https://github.com/paulmillr)", "<PERSON> (https://github.com/irnc)", "<PERSON> (https://github.com/phawk)", "<PERSON> <<EMAIL>> (https://github.com/psung)", "<PERSON><PERSON><PERSON>  <<EMAIL>> (https://github.com/pra85)", "qiu <PERSON>hui <qiuzu<PERSON>@gmail.com> (https://github.com/qiuzuhui)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/quangv)", "<PERSON><PERSON> (https://github.com/Rauno56)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/refack)", "<PERSON> <<EMAIL>> (https://github.com/rdingwall)", "<PERSON> (https://github.com/Richard<PERSON>)", "Rico Sta. Cruz <<EMAIL>> (https://github.com/rstacruz)", "<PERSON> (https://github.com/Alaneor)", "<PERSON> <<EMAIL>> (https://github.com/Rob--W)", "Romain (https://github.com/rprieto)", "<PERSON> <<EMAIL>> (https://github.com/roman-neuhauser)", "<PERSON>man (https://github.com/defunctzombie)", "<PERSON> <<EMAIL>> (https://github.com/devdazed)", "<PERSON> (https://github.com/rmunson)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/rulikkk)", "<PERSON> <<EMAIL>> (https://github.com/ryan-shaw)", "<PERSON> (https://github.com/ryedog)", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/tricknotes)", "ryym (https://github.com/ryym)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/shovon)", "Salvador de la Puente González <<EMAIL>> (https://github.com/delapuente)", "<PERSON> (https://github.com/smussell)", "<PERSON> <<EMAIL>> (https://github.com/ouhouhsami)", "<PERSON> <<EMAIL>> (https://github.com/kossnocorp)", "<PERSON> <<EMAIL>> (https://github.com/ScottFreeCode)", "<PERSON> <<EMAIL>> (https://github.com/slang800)", "seb vincent <<EMAIL>> (https://github.com/sebv)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/nulltask)", "<PERSON> (https://github.com/segrey)", "<PERSON> (https://github.com/taueres)", "<PERSON><PERSON> (https://github.com/bd82)", "<PERSON><PERSON><PERSON> (https://github.com/shaine)", "<PERSON><PERSON> (https://github.com/wsw0108)", "<PERSON> (https://github.com/simong)", "<PERSON> (https://github.com/sgoumaz)", "Sindre Sorhus <<EMAIL>> (https://github.com/sindresorhus)", "slientcloud <<EMAIL>> (https://github.com/silentcloud)", "<PERSON><PERSON> (https://github.com/iclanzan)", "<PERSON>a Opichal <<EMAIL>> (https://github.com/opichals)", "<PERSON> <<EMAIL>> (https://github.com/stephenmathieson)", "<PERSON> (https://github.com/spmason)", "<PERSON> <<EMAIL>> (https://github.com/<PERSON>-<PERSON>)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/sunesimonsen)", "<PERSON><PERSON><PERSON><PERSON> (https://github.com/slyg)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/nishigori)", "<PERSON> (https://github.com/tsgautier)", "<PERSON> (https://github.com/teddyzeenny)", "<PERSON> <https//@graingert.co.uk> (https://github.com/graingert)", "<PERSON> (https://github.com/timehat)", "<PERSON> <<EMAIL>> (https://github.com/TimothyGu)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/Krinkle)", "<PERSON><PERSON> <PERSON> <<EMAIL>> (https://github.com/tinganho)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/tj)", "<PERSON> <<EMAIL>> (https://github.com/Turbo87)", "<PERSON> <<EMAIL>> (https://github.com/airportyh)", "<PERSON> (https://github.com/tawdle)", "<PERSON> (https://github.com/tomhughes)", "<PERSON> (https://github.com/tmont)", "traleig1 (https://github.com/traleig1)", "<PERSON> <<EMAIL>> (https://github.com/travisjeffery)", "<PERSON> <<EMAIL>> (https://github.com/tysontate)", "<PERSON><PERSON> (https://github.com/avaly)", "<PERSON> <<EMAIL>> (https://github.com/pwnall)", "<PERSON> (https://github.com/vlazzle)", "<PERSON> <<EMAIL>> (https://github.com/callmevlad)", "<PERSON> (https://github.com/wlangstroth)", "<PERSON><PERSON> <PERSON> <<EMAIL>> (https://github.com/wilmoore)", "<PERSON> <<EMAIL>> (https://github.com/antoviaque)", "<PERSON> <<EMAIL>> (https://github.com/xdamman)", "<PERSON><PERSON> <<EMAIL>> (https://github.com/yaniswang)", "yuitest <<EMAIL>> (https://github.com/yuitest)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/zhiyelee)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/fool2fish)", "<PERSON><PERSON><PERSON> <firstname at lastname dot cc> (https://github.com/oker1)"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/mochajs/mocha.git"}, "bin": {"mocha": "./bin/mocha", "_mocha": "./bin/_mocha"}, "engines": {"node": ">= 4.0.0"}, "scripts": {"lint": "eslint . bin/*", "test": "make clean && make test", "prepublishOnly": "npm test && make clean && make mocha.js", "coveralls": "nyc report --reporter=text-lcov | coveralls", "prebuildDocs": "rm -rf docs/_dist && node scripts/docs-update-toc.js", "buildDocs": "bundle exec jekyll build --source ./docs --destination ./docs/_site --config ./docs/_config.yml --safe --drafts", "postbuildDocs": "buildProduction docs/_site/index.html --outroot docs/_dist --canonicalroot https://mochajs.org/ --optimizeimages --svgo --inlinehtmlimage 9400 --inlinehtmlscript 0 --asyncscripts && cp docs/_headers docs/_dist/_headers && node scripts/netlify-headers.js >> docs/_dist/_headers", "prewatchDocs": "node scripts/docs-update-toc.js", "watchDocs": "bundle exec jekyll serve --source ./docs --destination ./docs/_site --config ./docs/_config.yml --safe --drafts --watch"}, "dependencies": {"browser-stdout": "1.3.0", "commander": "2.11.0", "debug": "3.1.0", "diff": "3.3.1", "escape-string-regexp": "1.0.5", "glob": "7.1.2", "growl": "1.10.3", "he": "1.1.1", "mkdirp": "0.5.1", "supports-color": "4.4.0"}, "devDependencies": {"assert": "^1.4.1", "assetgraph-builder": "^5.6.4", "browserify": "^14.4.0", "buffer": "^4.9.1", "coffee-script": "^1.10.0", "coveralls": "^3.0.0", "cross-spawn": "^5.1.0", "eslint": "^4.8.0", "eslint-config-semistandard": "^11.0.0", "eslint-config-standard": "^10.2.1", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "expect.js": "^0.3.1", "karma": "^1.7.1", "karma-browserify": "^5.0.5", "karma-chrome-launcher": "^2.0.0", "karma-expect": "^1.1.2", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.4", "karma-phantomjs-launcher": "^1.0.4", "karma-sauce-launcher": "^1.2.0", "markdown-toc": "^1.2.0", "nyc": "^11.2.1", "rimraf": "^2.5.2", "svgo": "^0.7.2", "through2": "^2.0.1", "watchify": "^3.7.0"}, "files": ["bin", "images", "lib", "index.js", "mocha.css", "mocha.js", "browser-entry.js"], "browser": {"growl": "./lib/browser/growl.js", "tty": "./lib/browser/tty.js", "./index.js": "./browser-entry.js", "fs": false, "glob": false, "path": false, "supports-color": false, "buffer": "buffer"}, "homepage": "https://mochajs.org", "logo": "https://cldup.com/S9uQ-cOLYz.svg"}