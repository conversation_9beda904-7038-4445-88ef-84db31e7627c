'use strict';
/**
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file AipImageCensor
 * <AUTHOR>
 */
const BaseClient = require("./client/baseClient");
const RequestInfo = require("./client/requestInfo");
const objectTools = require("./util/objectTools");
const HttpClient = require("./http/httpClient");
const HttpClientJson = require("./http/httpClientExt");
const httpHeader = require("./const/httpHeader");
const CONTENT_TYPE_JSON = 'application/json';
const METHOD_POST = 'POST';
const PATH_USER_DEFINED = '/rest/2.0/solution/v1/img_censor/user_defined';
const PATH_ANTIPORN_GIF = '/rest/2.0/antiporn/v1/detect_gif';
const PATH_FACEAUDIT = '/rest/2.0/solution/v1/face_audit';
const PATH_COMBOCENSOR = '/api/v1/solution/direct/img_censor';
const PATH_REPORT = '/rpc/2.0/feedback/v1/report';
const PATH_ANTIPORN = '/rest/2.0/antiporn/v1/detect';
const PATH_ANTITERROR = '/rest/2.0/antiterror/v1/detect';
const scope = require('./const/devScope').DEFAULT;
/**
 * AipImageCensor类，构造调用图像审核对象
 *
 * @class
 * @extends BaseClient
 * @constructor
 * @param {string} appid appid.
 * @param {string} ak  access key.
 * @param {string} sk  security key.
 */
class AipImageCensor extends BaseClient {
    constructor(appId, ak, sk) {
        super(appId, ak, sk);
    }
    commonImpl(param) {
        let httpClient = new HttpClient();
        let apiUrl = param.targetPath;
        delete param.targetPath;
        let requestInfo = new RequestInfo(apiUrl, param, METHOD_POST);
        return this.doRequest(requestInfo, httpClient);
    }
    jsonRequestImpl(param) {
        let httpClient = new HttpClientJson();
        let apiUrl = param.targetPath;
        delete param.targetPath;
        let requestInfo = new RequestInfo(apiUrl, param, METHOD_POST, false, {
            [httpHeader.CONTENT_TYPE]: CONTENT_TYPE_JSON
        });
        return this.doRequest(requestInfo, httpClient);
    }
    antiPornGif(image, options) {
        let param = {
            image: image,
            targetPath: PATH_ANTIPORN_GIF
        };
        return this.commonImpl(objectTools.merge(param, options));
    }
    antiPorn(image, options) {
        let param = {
            image: image,
            targetPath: PATH_ANTIPORN
        };
        return this.commonImpl(objectTools.merge(param, options));
    }
    antiTerror(image, options) {
        let param = {
            image: image,
            targetPath: PATH_ANTITERROR
        };
        return this.commonImpl(objectTools.merge(param, options));
    }
    faceAudit(images, type, configId) {
        let param = { configId: configId };
        if (type === 'url') {
            images = images.map(function (elm) {
                return encodeURIComponent(elm);
            });
            param.imgUrls = images.join(',');
        }
        if (type === 'base64') {
            param.images = images.join(',');
        }
        param.targetPath = PATH_FACEAUDIT;
        return this.commonImpl(param);
    }
    imageCensorUserDefined(image, type) {
        let param = {};
        if (type === 'url') {
            param.imgUrl = image;
        }
        if (type === 'base64') {
            param.image = image;
        }
        param.targetPath = PATH_USER_DEFINED;
        return this.commonImpl(param);
    }
    imageCensorComb(image, type, scenes, scenesConf) {
        let param = {};
        if (type === 'url') {
            param.imgUrl = image;
        }
        if (type === 'base64') {
            param.image = image;
        }
        param.scenes = scenes;
        param.sceneConf = scenesConf;
        param.targetPath = PATH_COMBOCENSOR;
        return this.jsonRequestImpl(param);
    }
    report(feedback) {
        let param = {
            feedback,
            targetPath: PATH_REPORT
        };
        return this.jsonRequestImpl(param);
    }
}
exports.default = AipImageCensor;
// @ts-ignore
Object.assign(AipImageCensor, exports);
module.exports = AipImageCensor;
//# sourceMappingURL=data:application/json;base64,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