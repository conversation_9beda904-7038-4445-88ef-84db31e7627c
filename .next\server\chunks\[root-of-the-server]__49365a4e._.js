module.exports = {

"[project]/.next-internal/server/app/api/test-ocr/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/test-ocr/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
async function POST(request) {
    try {
        const formData = await request.formData();
        const imageFile = formData.get('image');
        if (!imageFile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No image file provided'
            }, {
                status: 400
            });
        }
        // 验证文件类型
        if (!imageFile.type.startsWith('image/')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid file type. Please upload an image.'
            }, {
                status: 400
            });
        }
        // 模拟OCR结果 - 返回一些常见的测试文字
        const mockResult = {
            text: 'SAMPLE TEXT WATERMARK COPYRIGHT CONFIDENTIAL 测试 水印 版权',
            confidence: 95,
            words: [
                {
                    text: 'SAMPLE',
                    confidence: 98,
                    bbox: {
                        x0: 100,
                        y0: 50,
                        x1: 180,
                        y1: 80
                    }
                },
                {
                    text: 'TEXT',
                    confidence: 97,
                    bbox: {
                        x0: 200,
                        y0: 50,
                        x1: 250,
                        y1: 80
                    }
                },
                {
                    text: 'WATERMARK',
                    confidence: 96,
                    bbox: {
                        x0: 300,
                        y0: 100,
                        x1: 420,
                        y1: 130
                    }
                },
                {
                    text: 'COPYRIGHT',
                    confidence: 95,
                    bbox: {
                        x0: 100,
                        y0: 150,
                        x1: 220,
                        y1: 180
                    }
                },
                {
                    text: 'CONFIDENTIAL',
                    confidence: 94,
                    bbox: {
                        x0: 250,
                        y0: 150,
                        x1: 380,
                        y1: 180
                    }
                },
                {
                    text: '测试',
                    confidence: 93,
                    bbox: {
                        x0: 100,
                        y0: 200,
                        x1: 140,
                        y1: 230
                    }
                },
                {
                    text: '水印',
                    confidence: 92,
                    bbox: {
                        x0: 160,
                        y0: 200,
                        x1: 200,
                        y1: 230
                    }
                },
                {
                    text: '版权',
                    confidence: 91,
                    bbox: {
                        x0: 220,
                        y0: 200,
                        x1: 260,
                        y1: 230
                    }
                }
            ]
        };
        // 添加延迟模拟真实API调用
        await new Promise((resolve)=>setTimeout(resolve, 1000));
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(mockResult);
    } catch (error) {
        console.error('Test OCR processing error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Test OCR processing failed'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__49365a4e._.js.map