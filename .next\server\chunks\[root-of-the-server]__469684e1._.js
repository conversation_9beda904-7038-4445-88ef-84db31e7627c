module.exports = {

"[project]/.next-internal/server/app/api/ocr/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/ocr/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
;
// 百度OCR配置
const BAIDU_OCR_CONFIG = {
    APP_ID: process.env.BAIDU_APP_ID || '',
    API_KEY: process.env.BAIDU_API_KEY || '',
    SECRET_KEY: process.env.BAIDU_SECRET_KEY || ''
};
// 获取百度访问令牌
async function getBaiduAccessToken() {
    const tokenUrl = 'https://aip.baidubce.com/oauth/2.0/token';
    const params = new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: BAIDU_OCR_CONFIG.API_KEY,
        client_secret: BAIDU_OCR_CONFIG.SECRET_KEY
    });
    try {
        const response = await fetch(`${tokenUrl}?${params}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });
        const data = await response.json();
        if (data.access_token) {
            return data.access_token;
        } else {
            throw new Error('Failed to get access token: ' + JSON.stringify(data));
        }
    } catch (error) {
        console.error('Error getting Baidu access token:', error);
        throw new Error('Failed to authenticate with Baidu OCR');
    }
}
// 调用百度OCR API
async function callBaiduOCR(imageBase64, accessToken) {
    const ocrUrl = 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic';
    const formData = new URLSearchParams({
        image: imageBase64,
        language_type: 'CHN_ENG'
    });
    try {
        const response = await fetch(`${ocrUrl}?access_token=${accessToken}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: formData
        });
        const data = await response.json();
        if (data.error_code) {
            throw new Error(`Baidu OCR API error: ${data.error_msg}`);
        }
        return data;
    } catch (error) {
        console.error('Error calling Baidu OCR:', error);
        throw new Error('Failed to process image with Baidu OCR');
    }
}
// 将图片文件转换为Base64
function fileToBase64(file) {
    return new Promise((resolve, reject)=>{
        const reader = new FileReader();
        reader.onload = ()=>{
            const result = reader.result;
            // 移除data:image/...;base64,前缀
            const base64 = result.split(',')[1];
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}
async function POST(request) {
    try {
        // 检查环境变量
        if (!BAIDU_OCR_CONFIG.API_KEY || !BAIDU_OCR_CONFIG.SECRET_KEY) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Baidu OCR credentials not configured'
            }, {
                status: 500
            });
        }
        const formData = await request.formData();
        const imageFile = formData.get('image');
        if (!imageFile) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'No image file provided'
            }, {
                status: 400
            });
        }
        // 验证文件类型
        if (!imageFile.type.startsWith('image/')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Invalid file type. Please upload an image.'
            }, {
                status: 400
            });
        }
        // 验证文件大小 (百度OCR限制4MB)
        if (imageFile.size > 4 * 1024 * 1024) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Image file too large. Maximum size is 4MB.'
            }, {
                status: 400
            });
        }
        // 转换为Base64
        const imageBuffer = await imageFile.arrayBuffer();
        const imageBase64 = Buffer.from(imageBuffer).toString('base64');
        // 获取访问令牌
        const accessToken = await getBaiduAccessToken();
        // 调用百度OCR
        const ocrResult = await callBaiduOCR(imageBase64, accessToken);
        // 转换为统一格式
        const result = {
            text: ocrResult.words_result.map((item)=>item.words).join(' '),
            confidence: 95,
            words: ocrResult.words_result.map((item)=>({
                    text: item.words || '',
                    confidence: 95,
                    bbox: {
                        x0: item.location?.left || 0,
                        y0: item.location?.top || 0,
                        x1: (item.location?.left || 0) + (item.location?.width || 0),
                        y1: (item.location?.top || 0) + (item.location?.height || 0)
                    }
                }))
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('OCR processing error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: error instanceof Error ? error.message : 'OCR processing failed'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__469684e1._.js.map