# 快速开始指南

## 🚀 立即体验（无需配置）

应用程序现在已经集成了百度OCR，并提供了测试模式，您可以立即体验所有功能！

### 1. 启动应用程序
应用程序已经在运行：
- **本地地址**: http://localhost:3000
- **网络地址**: http://************:3000

### 2. 测试功能

#### 📸 准备测试图片
创建或找一张包含以下文字的图片：
- 英文：SAMPLE, TEXT, WATERMARK, COPYRIGHT, CONFIDENTIAL
- 中文：测试, 水印, 版权

#### 📝 使用提供的测试文件
项目中已经包含了 `test-words.txt` 文件，包含常见的需要移除的文字。

#### 🎯 测试步骤
1. **上传图片**: 拖拽或点击上传包含文字的图片
2. **上传文本文件**: 使用 `test-words.txt` 或创建自己的文本文件
3. **开始处理**: 点击"Process Image"按钮
4. **查看结果**: 应用程序会显示原图和处理后的对比
5. **下载结果**: 点击下载按钮保存处理后的图片

### 3. 功能特点

#### 🔥 当前可用功能
- ✅ **智能OCR识别**: 自动检测图片中的文字
- ✅ **精确文字匹配**: 支持完全匹配和模糊匹配
- ✅ **智能文字移除**: 上下文感知的填充技术
- ✅ **自定义选项**: 填充颜色、边距、模糊效果
- ✅ **批量处理**: 一次处理多个图片
- ✅ **进度跟踪**: 实时显示处理进度
- ✅ **快捷键**: Ctrl+Enter 处理, Ctrl+D 下载

#### 🎨 处理选项
- **填充颜色**: 选择合适的颜色填充移除的文字区域
- **智能填充**: 根据周围像素自动选择最佳颜色
- **边距控制**: 调整文字周围的填充范围 (0-10px)
- **模糊效果**: 添加轻微模糊让填充更自然 (0-5px)

### 4. 测试模式说明

#### 🧪 当前运行模式
应用程序当前运行在**测试模式**下：
- 使用模拟OCR数据进行演示
- 可以完整体验所有功能
- 处理结果基于预设的测试文字

#### 🔧 切换到生产模式
要使用真实的百度OCR API：
1. 按照 `BAIDU_OCR_SETUP.md` 配置百度OCR密钥
2. 重启应用程序
3. 应用程序会自动切换到百度OCR模式

### 5. 快捷操作

#### ⌨️ 键盘快捷键
- `Ctrl + Enter`: 快速开始处理图片
- `Ctrl + D`: 快速下载处理结果
- `Esc`: 清除提示消息

#### 🖱️ 鼠标操作
- 拖拽文件到上传区域
- 点击颜色选择器自定义填充颜色
- 拖动滑块调整处理参数

### 6. 常见问题

#### ❓ 为什么显示"使用测试OCR"？
这是正常的！应用程序在没有配置百度OCR密钥时会自动使用测试模式，您仍然可以体验完整功能。

#### ❓ 如何获得更好的识别效果？
1. 配置百度OCR API（参考 `BAIDU_OCR_SETUP.md`）
2. 使用高质量、清晰的图片
3. 确保文字对比度足够高

#### ❓ 支持哪些图片格式？
- JPG, JPEG, PNG, BMP, GIF, WebP
- 最大文件大小：10MB（百度OCR模式下为4MB）

### 7. 下一步

#### 🎯 体验完整功能
1. 测试不同类型的图片
2. 尝试批量处理模式
3. 调整各种处理选项
4. 使用快捷键提高效率

#### 🔧 配置生产环境
如果您想要最佳的OCR识别效果，建议配置百度OCR API：
- 查看 `BAIDU_OCR_SETUP.md` 获取详细配置说明
- 百度OCR提供每月1000次免费调用
- 识别精度可达95%以上

---

🎉 **开始体验吧！** 应用程序已经准备就绪，您可以立即开始测试所有功能。
