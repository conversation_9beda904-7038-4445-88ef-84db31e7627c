import { TextMatch } from './ocr';

export interface ProcessingOptions {
  fillColor?: string;
  padding?: number;
  blurRadius?: number;
}

export class ImageProcessor {
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;

  private initializeCanvas() {
    if (typeof window === 'undefined') {
      throw new Error('ImageProcessor can only be used in the browser');
    }

    if (!this.canvas) {
      this.canvas = document.createElement('canvas');
      const context = this.canvas.getContext('2d');
      if (!context) {
        throw new Error('Failed to get canvas 2D context');
      }
      this.ctx = context;
    }
  }

  async loadImage(file: File): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      const url = URL.createObjectURL(file);
      
      img.onload = () => {
        URL.revokeObjectURL(url);
        resolve(img);
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load image'));
      };
      
      img.src = url;
    });
  }

  async removeTextFromImage(
    imageFile: File,
    textMatches: TextMatch[],
    options: ProcessingOptions = {}
  ): Promise<Blob> {
    this.initializeCanvas();
    if (!this.canvas || !this.ctx) {
      throw new Error('Canvas not initialized');
    }

    const img = await this.loadImage(imageFile);

    // Set canvas dimensions to match image
    this.canvas.width = img.width;
    this.canvas.height = img.height;

    // Draw the original image
    this.ctx.drawImage(img, 0, 0);

    // Remove text by covering matched areas
    await this.removeTextAreas(textMatches, options);

    return new Promise((resolve, reject) => {
      this.canvas!.toBlob((blob) => {
        if (blob) {
          resolve(blob);
        } else {
          reject(new Error('Failed to create processed image blob'));
        }
      }, 'image/png');
    });
  }

  private async removeTextAreas(textMatches: TextMatch[], options: ProcessingOptions): Promise<void> {
    const { fillColor = '#FFFFFF', padding = 2, blurRadius = 0 } = options;
    
    for (const match of textMatches) {
      const { bbox } = match;
      
      // Add padding to the bounding box
      const x = Math.max(0, bbox.x0 - padding);
      const y = Math.max(0, bbox.y0 - padding);
      const width = Math.min(this.canvas.width - x, bbox.x1 - bbox.x0 + 2 * padding);
      const height = Math.min(this.canvas.height - y, bbox.y1 - bbox.y0 + 2 * padding);
      
      if (blurRadius > 0) {
        // Apply blur effect for more natural text removal
        await this.applyInpainting(x, y, width, height);
      } else {
        // Simple fill with solid color
        this.ctx.fillStyle = fillColor;
        this.ctx.fillRect(x, y, width, height);
      }
    }
  }

  private async applyInpainting(x: number, y: number, width: number, height: number): Promise<void> {
    // Get surrounding pixels for color sampling
    const surroundingData = this.getSurroundingPixels(x, y, width, height);
    
    // Calculate average color from surrounding pixels
    const avgColor = this.calculateAverageColor(surroundingData);
    
    // Create gradient fill for more natural appearance
    const gradient = this.ctx.createLinearGradient(x, y, x + width, y + height);
    gradient.addColorStop(0, avgColor);
    gradient.addColorStop(1, this.adjustBrightness(avgColor, 0.9));
    
    this.ctx.fillStyle = gradient;
    this.ctx.fillRect(x, y, width, height);
    
    // Apply slight blur effect
    this.applyGaussianBlur(x, y, width, height, 1);
  }

  private getSurroundingPixels(x: number, y: number, width: number, height: number): ImageData {
    const margin = 5;
    const surroundX = Math.max(0, x - margin);
    const surroundY = Math.max(0, y - margin);
    const surroundWidth = Math.min(this.canvas.width - surroundX, width + 2 * margin);
    const surroundHeight = Math.min(this.canvas.height - surroundY, height + 2 * margin);
    
    return this.ctx.getImageData(surroundX, surroundY, surroundWidth, surroundHeight);
  }

  private calculateAverageColor(imageData: ImageData): string {
    const data = imageData.data;
    let r = 0, g = 0, b = 0;
    let pixelCount = 0;
    
    for (let i = 0; i < data.length; i += 4) {
      r += data[i];
      g += data[i + 1];
      b += data[i + 2];
      pixelCount++;
    }
    
    r = Math.round(r / pixelCount);
    g = Math.round(g / pixelCount);
    b = Math.round(b / pixelCount);
    
    return `rgb(${r}, ${g}, ${b})`;
  }

  private adjustBrightness(color: string, factor: number): string {
    const rgb = color.match(/\d+/g);
    if (!rgb) return color;
    
    const r = Math.round(parseInt(rgb[0]) * factor);
    const g = Math.round(parseInt(rgb[1]) * factor);
    const b = Math.round(parseInt(rgb[2]) * factor);
    
    return `rgb(${r}, ${g}, ${b})`;
  }

  private applyGaussianBlur(x: number, y: number, width: number, height: number, radius: number): void {
    // Simple box blur approximation of Gaussian blur
    const imageData = this.ctx.getImageData(x, y, width, height);
    const blurred = this.boxBlur(imageData, radius);
    this.ctx.putImageData(blurred, x, y);
  }

  private boxBlur(imageData: ImageData, radius: number): ImageData {
    const data = new Uint8ClampedArray(imageData.data);
    const width = imageData.width;
    const height = imageData.height;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        let r = 0, g = 0, b = 0, a = 0;
        let count = 0;
        
        for (let dy = -radius; dy <= radius; dy++) {
          for (let dx = -radius; dx <= radius; dx++) {
            const nx = x + dx;
            const ny = y + dy;
            
            if (nx >= 0 && nx < width && ny >= 0 && ny < height) {
              const idx = (ny * width + nx) * 4;
              r += imageData.data[idx];
              g += imageData.data[idx + 1];
              b += imageData.data[idx + 2];
              a += imageData.data[idx + 3];
              count++;
            }
          }
        }
        
        const idx = (y * width + x) * 4;
        data[idx] = r / count;
        data[idx + 1] = g / count;
        data[idx + 2] = b / count;
        data[idx + 3] = a / count;
      }
    }
    
    return new ImageData(data, width, height);
  }

  getCanvas(): HTMLCanvasElement {
    return this.canvas;
  }
}

// Create a function to get the image processor instance (lazy initialization)
let imageProcessorInstance: ImageProcessor | null = null;

export function getImageProcessor(): ImageProcessor {
  if (!imageProcessorInstance) {
    imageProcessorInstance = new ImageProcessor();
  }
  return imageProcessorInstance;
}
