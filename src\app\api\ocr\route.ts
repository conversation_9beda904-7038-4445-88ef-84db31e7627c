import { NextRequest, NextResponse } from 'next/server';

// 百度OCR配置
const BAIDU_OCR_CONFIG = {
  APP_ID: process.env.BAIDU_APP_ID || '',
  API_KEY: process.env.BAIDU_API_KEY || '',
  SECRET_KEY: process.env.BAIDU_SECRET_KEY || '',
};

interface BaiduOCRWord {
  words: string;
  location: {
    left: number;
    top: number;
    width: number;
    height: number;
  };
}

interface BaiduOCRResponse {
  words_result: BaiduOCRWord[];
  words_result_num: number;
}

// 获取百度访问令牌
async function getBaiduAccessToken(): Promise<string> {
  const tokenUrl = 'https://aip.baidubce.com/oauth/2.0/token';
  const params = new URLSearchParams({
    grant_type: 'client_credentials',
    client_id: BAIDU_OCR_CONFIG.API_KEY,
    client_secret: BAIDU_OCR_CONFIG.SECRET_KEY,
  });

  try {
    const response = await fetch(`${tokenUrl}?${params}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    const data = await response.json();
    
    if (data.access_token) {
      return data.access_token;
    } else {
      throw new Error('Failed to get access token: ' + JSON.stringify(data));
    }
  } catch (error) {
    console.error('Error getting Baidu access token:', error);
    throw new Error('Failed to authenticate with Baidu OCR');
  }
}

// 调用百度OCR API
async function callBaiduOCR(imageBase64: string, accessToken: string): Promise<BaiduOCRResponse> {
  const ocrUrl = 'https://aip.baidubce.com/rest/2.0/ocr/v1/general_basic';
  
  const formData = new URLSearchParams({
    image: imageBase64,
    language_type: 'CHN_ENG', // 中英文混合识别
  });

  try {
    const response = await fetch(`${ocrUrl}?access_token=${accessToken}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: formData,
    });

    const data = await response.json();
    
    if (data.error_code) {
      throw new Error(`Baidu OCR API error: ${data.error_msg}`);
    }

    return data;
  } catch (error) {
    console.error('Error calling Baidu OCR:', error);
    throw new Error('Failed to process image with Baidu OCR');
  }
}

// 将图片文件转换为Base64
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      const result = reader.result as string;
      // 移除data:image/...;base64,前缀
      const base64 = result.split(',')[1];
      resolve(base64);
    };
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });
}

export async function POST(request: NextRequest) {
  try {
    // 检查环境变量
    if (!BAIDU_OCR_CONFIG.API_KEY || !BAIDU_OCR_CONFIG.SECRET_KEY) {
      return NextResponse.json(
        { error: 'Baidu OCR credentials not configured' },
        { status: 500 }
      );
    }

    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    
    if (!imageFile) {
      return NextResponse.json(
        { error: 'No image file provided' },
        { status: 400 }
      );
    }

    // 验证文件类型
    if (!imageFile.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an image.' },
        { status: 400 }
      );
    }

    // 验证文件大小 (百度OCR限制4MB)
    if (imageFile.size > 4 * 1024 * 1024) {
      return NextResponse.json(
        { error: 'Image file too large. Maximum size is 4MB.' },
        { status: 400 }
      );
    }

    // 转换为Base64
    const imageBuffer = await imageFile.arrayBuffer();
    const imageBase64 = Buffer.from(imageBuffer).toString('base64');

    // 获取访问令牌
    const accessToken = await getBaiduAccessToken();

    // 调用百度OCR
    const ocrResult = await callBaiduOCR(imageBase64, accessToken);

    // 转换为统一格式
    const result = {
      text: ocrResult.words_result.map(item => item.words).join(' '),
      confidence: 95, // 百度OCR通常有很高的准确率
      words: ocrResult.words_result.map(item => ({
        text: item.words,
        confidence: 95,
        bbox: {
          x0: item.location.left,
          y0: item.location.top,
          x1: item.location.left + item.location.width,
          y1: item.location.top + item.location.height,
        }
      }))
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('OCR processing error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'OCR processing failed' },
      { status: 500 }
    );
  }
}
