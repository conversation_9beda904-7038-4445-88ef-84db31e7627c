import { NextRequest, NextResponse } from 'next/server';

// 测试OCR API - 返回模拟数据
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const imageFile = formData.get('image') as File;
    
    if (!imageFile) {
      return NextResponse.json(
        { error: 'No image file provided' },
        { status: 400 }
      );
    }

    // 验证文件类型
    if (!imageFile.type.startsWith('image/')) {
      return NextResponse.json(
        { error: 'Invalid file type. Please upload an image.' },
        { status: 400 }
      );
    }

    // 模拟OCR结果 - 返回更多中英文混合的测试文字
    const mockResult = {
      text: 'TOP SAMPLE TEXT WATERMARK COPYRIGHT CONFIDENTIAL 经典 居家 测试 水印 版权 机密 草稿 临时 DRAFT BETA',
      confidence: 95,
      words: [
        {
          text: 'TOP',
          confidence: 98,
          bbox: { x0: 50, y0: 20, x1: 90, y1: 50 }
        },
        {
          text: 'SAMPLE',
          confidence: 98,
          bbox: { x0: 100, y0: 50, x1: 180, y1: 80 }
        },
        {
          text: 'TEXT',
          confidence: 97,
          bbox: { x0: 200, y0: 50, x1: 250, y1: 80 }
        },
        {
          text: 'WATERMARK',
          confidence: 96,
          bbox: { x0: 300, y0: 100, x1: 420, y1: 130 }
        },
        {
          text: 'COPYRIGHT',
          confidence: 95,
          bbox: { x0: 100, y0: 150, x1: 220, y1: 180 }
        },
        {
          text: 'CONFIDENTIAL',
          confidence: 94,
          bbox: { x0: 250, y0: 150, x1: 380, y1: 180 }
        },
        {
          text: '经典',
          confidence: 93,
          bbox: { x0: 50, y0: 200, x1: 90, y1: 230 }
        },
        {
          text: '居家',
          confidence: 92,
          bbox: { x0: 100, y0: 200, x1: 140, y1: 230 }
        },
        {
          text: '测试',
          confidence: 93,
          bbox: { x0: 150, y0: 200, x1: 190, y1: 230 }
        },
        {
          text: '水印',
          confidence: 92,
          bbox: { x0: 200, y0: 200, x1: 240, y1: 230 }
        },
        {
          text: '版权',
          confidence: 91,
          bbox: { x0: 250, y0: 200, x1: 290, y1: 230 }
        },
        {
          text: '机密',
          confidence: 90,
          bbox: { x0: 300, y0: 200, x1: 340, y1: 230 }
        },
        {
          text: '草稿',
          confidence: 89,
          bbox: { x0: 350, y0: 200, x1: 390, y1: 230 }
        },
        {
          text: '临时',
          confidence: 88,
          bbox: { x0: 400, y0: 200, x1: 440, y1: 230 }
        },
        {
          text: 'DRAFT',
          confidence: 87,
          bbox: { x0: 100, y0: 250, x1: 150, y1: 280 }
        },
        {
          text: 'BETA',
          confidence: 86,
          bbox: { x0: 160, y0: 250, x1: 200, y1: 280 }
        }
      ]
    };

    // 添加延迟模拟真实API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    return NextResponse.json(mockResult);

  } catch (error) {
    console.error('Test OCR processing error:', error);
    return NextResponse.json(
      { error: 'Test OCR processing failed' },
      { status: 500 }
    );
  }
}
