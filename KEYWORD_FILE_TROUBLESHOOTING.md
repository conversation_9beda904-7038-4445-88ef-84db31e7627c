# 关键词文件问题诊断指南

## 🚨 问题描述
用户反馈：更新关键词文件内容后，匹配就出错了。

## 🔍 可能的原因

### 1. 文件编码问题 ⚠️
**最常见原因**: 文件保存时使用了错误的编码格式

#### 症状：
- 中文字符显示为乱码
- 文件读取失败
- 匹配算法无法识别中文

#### 解决方案：
- 确保文件保存为 **UTF-8 编码**
- 避免使用 ANSI 或 GBK 编码

### 2. BOM (Byte Order Mark) 问题
**症状**: 第一个关键词匹配失败

#### 解决方案：
- 使用"UTF-8 无BOM"格式保存
- 应用程序现在会自动检测和移除BOM

### 3. 隐藏字符问题
**症状**: 看起来相同的词汇无法匹配

#### 常见隐藏字符：
- 零宽度空格 (U+200B)
- 不间断空格 (U+00A0)
- 制表符和回车符

### 4. 文件格式问题
**症状**: 文件读取异常

#### 检查项目：
- 确保是纯文本文件 (.txt)
- 每行一个关键词
- 没有多余的空行

## 🛠️ 诊断工具

### 已添加的诊断功能：
1. **文件信息检测**: 文件名、大小、类型
2. **BOM检测**: 自动检测和移除BOM标记
3. **内容分析**: 显示原始内容和处理后内容
4. **编码验证**: 检查中英文字符
5. **逐行解析**: 显示每行的原始和处理后内容

### 查看诊断信息：
1. 打开浏览器开发者工具 (F12)
2. 切换到 Console 标签
3. 上传关键词文件
4. 查看详细的诊断日志

## 🧪 测试步骤

### 第一步：使用标准文件测试
1. 使用项目中的 `keywords-standard.txt` 文件
2. 这个文件确保是正确的UTF-8格式
3. 测试是否能正常工作

### 第二步：检查您的文件
1. 上传您修改的关键词文件
2. 查看控制台的诊断信息
3. 检查是否有编码或格式问题

### 第三步：文件重新创建
如果问题持续，请：
1. 创建新的文本文件
2. 手动输入关键词（每行一个）
3. 保存为UTF-8编码
4. 重新测试

## 📋 正确的文件格式

### 文件要求：
- **编码**: UTF-8 (无BOM)
- **格式**: 纯文本 (.txt)
- **结构**: 每行一个关键词
- **换行**: 使用标准换行符

### 示例内容：
```
TOP
经典
居家
文字
SAMPLE
```

## 🔧 常见问题解决

### 问题1: 中文乱码
**解决**: 
- 使用支持UTF-8的编辑器 (如VS Code, Notepad++)
- 保存时选择UTF-8编码

### 问题2: 第一个词匹配失败
**解决**: 
- 检查BOM标记
- 应用程序现在会自动处理

### 问题3: 某些词无法匹配
**解决**: 
- 检查隐藏字符
- 重新输入该词汇

### 问题4: 文件读取失败
**解决**: 
- 确保文件是纯文本格式
- 检查文件权限

## 🎯 推荐工作流程

### 创建关键词文件的最佳实践：
1. **使用专业编辑器**: VS Code, Notepad++, Sublime Text
2. **设置编码**: UTF-8 无BOM
3. **逐行添加**: 每行一个关键词
4. **保存验证**: 重新打开检查内容
5. **测试上传**: 在应用中测试

### 修改现有文件：
1. **备份原文件**
2. **使用UTF-8编辑器打开**
3. **进行修改**
4. **保存为UTF-8格式**
5. **测试验证**

## 📞 获取帮助

如果问题仍然存在，请提供：
1. 浏览器控制台的完整诊断日志
2. 您使用的文本编辑器
3. 文件的具体内容
4. 操作系统信息

现在应用程序有了完整的诊断功能，可以帮助快速定位问题！
