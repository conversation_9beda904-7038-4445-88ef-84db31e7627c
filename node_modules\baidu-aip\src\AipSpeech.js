'use strict';
/**
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file AipSpeech
 * <AUTHOR>
 */
const BaseClient = require("./client/baseClient");
const RequestInfo = require("./client/requestInfo");
const objectTools = require("./util/objectTools");
const HttpClientVoiceASR = require("./http/httpClientVoiceASR");
const HttpClientVoiceTTS = require("./http/httpClientVoiceTTS");
const code = require("./const/code");
const httpHeader = require("./const/httpHeader");
const METHOD_POST = 'POST';
const CONTENT_TYPE_JSON = 'application/json';
const HOST_VOP = 'vop.baidu.com';
const HOST_TSN = 'tsn.baidu.com';
const PATH_VOP = '/server_api';
const PATH_TTS = '/text2audio';
/**
 * AipSpeech类，构造调用语音接口
 *
 * @class
 * @extends BaseClient
 * @constructor
 * @param {string} appid appid.
 * @param {string} ak  access key.
 * @param {string} sk  security key.
 */
class AipSpeech extends BaseClient {
    constructor(appId, ak, sk) {
        // 在speech.baidu.com上创建的应用需要跳过此项权限检查
        super(appId, ak, sk, { isSkipScopeCheck: true });
    }
    recognize(buffer, format, rate, options) {
        let param = {
            speech: buffer && buffer.toString(code.BASE64),
            format: format,
            rate: rate,
            channel: 1,
            len: buffer && buffer.toString(code.BIN).length
        };
        return this.asrImpl(objectTools.merge(param, options));
    }
    recognizeByUrl(url, callback, format, rate, options) {
        let param = {
            url: url,
            format: format,
            rate: rate,
            channel: 1,
            callback: callback
        };
        return this.asrImpl(objectTools.merge(param, options));
    }
    asrImpl(param) {
        let httpClient = new HttpClientVoiceASR();
        let requestInfo = new RequestInfo(PATH_VOP, param, METHOD_POST, false, {
            [httpHeader.CONTENT_TYPE]: CONTENT_TYPE_JSON
        });
        requestInfo.setHost(HOST_VOP);
        return this.doRequest(requestInfo, httpClient);
    }
    text2audio(text, options) {
        let param = {
            tex: text,
            lan: 'zh',
            ctp: 1
        };
        return this.ttsImpl(objectTools.merge(param, options));
    }
    ttsImpl(param) {
        let httpClient = new HttpClientVoiceTTS();
        let requestInfo = new RequestInfo(PATH_TTS, param, METHOD_POST, true);
        requestInfo.setHost(HOST_TSN);
        return this.doRequest(requestInfo, httpClient);
    }
}
exports.default = AipSpeech;
// @ts-ignore
Object.assign(AipSpeech, exports);
module.exports = AipSpeech;
//# sourceMappingURL=data:application/json;base64,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