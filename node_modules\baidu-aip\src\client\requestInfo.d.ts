/**
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file requestInfo
 * <AUTHOR>
 */
import HttpHeader = require('../const/httpHeader');
import { Headers } from "request";
import DevAuthToken = require('../auth/devAuthToken');
declare const HOST_DEFAULT = "aip.baidubce.com";
/**
* RequestInfo类
* 构造供request库调用的请求信息对象
*
* @constructor
*/
declare class RequestInfo<H extends Headers = Headers> {
    isHttp: boolean;
    method: string;
    host: string | typeof HOST_DEFAULT;
    path: any;
    params: any;
    createDate: Date;
    mergeHeaders: H;
    devAccessToken: DevAuthToken;
    headers: H & Headers & {
        [k in keyof typeof HttpHeader]?: any;
    };
    constructor(path: any, params: any, method: string, isHttp?: boolean, headers?: H);
    setHost(host: any): void;
    initCommonHeader(): void;
    makeDevOptions(devAccessToken: DevAuthToken): void;
    makeBceOptions(ak: string, sk: string): void;
    getUTCDateStr(): string;
    getAccessToken(): string;
    getUrl(): string;
    getPureUrl(): string;
    getHttpsUrl(): string;
    getHttpUrl(): string;
}
export default RequestInfo;
export = RequestInfo;
