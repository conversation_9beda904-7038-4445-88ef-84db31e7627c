'use strict';
/**
 * Copyright (c) 2017 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file devAuth
 * <AUTHOR>
 */
const HttpClient = require("../http/httpClient");
const DevAuthToken = require("./devAuthToken");
const objectTool = require("../util/objectTools");
const OPENAPI_TOKEN_URL = 'https://aip.baidubce.com/oauth/2.0/token';
const OPENAPI_GRANTTYPE_CLIENT = 'client_credentials';
const REQUEST_TOKEN_METHOD = 'post';
/**
* devAuth类
* 百度开发者token获取类
*
* @constructor
* @param {string} ak API Key.
* @param {string} sk Secret Key.
*/
class DevAuth {
    constructor(ak, sk) {
        this.ak = ak;
        this.sk = sk;
        this.httpClient = new HttpClient();
    }
    gotData(data) {
        // 如果返回数据非法，此时data为请求数据body
        if (!objectTool.isObject(data)) {
            throw { errorType: DevAuth.EVENT_ERRTYPE_ILLEGAL_RESPONSE, error: data };
        }
        // 如果获取token失败，数据是合法的错误数据
        if (data.error) {
            throw { errorType: DevAuth.EVENT_ERRTYPE_NORMAL, error: data.error };
        }
        else {
            // 获取token成功
            return new DevAuthToken(data.access_token, data.expires_in, data.scope);
        }
    }
    gotDataError(err) {
        // request.js内部错误封装下返回
        throw {
            errorType: DevAuth.EVENT_ERRTYPE_NETWORK,
            error: err
        };
    }
    getToken() {
        let options = {
            url: OPENAPI_TOKEN_URL,
            method: REQUEST_TOKEN_METHOD,
            form: {
                grant_type: OPENAPI_GRANTTYPE_CLIENT,
                client_id: this.ak,
                client_secret: this.sk
            }
        };
        // @ts-ignore
        return this.httpClient.req(options).then(this.gotData.bind(this), this.gotDataError.bind(this));
    }
}
(function (DevAuth) {
    DevAuth.EVENT_ERRTYPE_ILLEGAL_RESPONSE = "ERRTYPE_ILLEGAL_RESPONSE";
    DevAuth.EVENT_ERRTYPE_NETWORK = "ERRTYPE_NETWORK";
    DevAuth.EVENT_ERRTYPE_NORMAL = "ERRTYPE_NORMAL";
})(DevAuth || (DevAuth = {}));
exports.default = DevAuth;
// @ts-ignore
Object.assign(DevAuth, exports);
module.exports = DevAuth;
//# sourceMappingURL=data:application/json;base64,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