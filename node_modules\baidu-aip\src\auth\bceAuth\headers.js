"use strict";
/**
 * Copyright (c) 2014 Baidu.com, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file src/headers.js
 * <AUTHOR>
 */
Object.defineProperty(exports, "__esModule", { value: true });
/* eslint-env node */
exports.CONTENT_TYPE = 'Content-Type';
exports.CONTENT_LENGTH = 'Content-Length';
exports.CONTENT_MD5 = 'Content-MD5';
exports.CONTENT_ENCODING = 'Content-Encoding';
exports.CONTENT_DISPOSITION = 'Content-Disposition';
exports.ETAG = 'ETag';
exports.CONNECTION = 'Connection';
exports.HOST = 'Host';
exports.USER_AGENT = 'User-Agent';
exports.CACHE_CONTROL = 'Cache-Control';
exports.EXPIRES = 'Expires';
exports.AUTHORIZATION = 'Authorization';
exports.X_BCE_DATE = 'x-bce-date';
exports.X_BCE_ACL = 'x-bce-acl';
exports.X_BCE_REQUEST_ID = 'x-bce-request-id';
exports.X_BCE_CONTENT_SHA256 = 'x-bce-content-sha256';
exports.X_BCE_OBJECT_ACL = 'x-bce-object-acl';
exports.X_BCE_OBJECT_GRANT_READ = 'x-bce-object-grant-read';
exports.X_HTTP_HEADERS = 'http_headers';
exports.X_BODY = 'body';
exports.X_STATUS_CODE = 'status_code';
exports.X_MESSAGE = 'message';
exports.X_CODE = 'code';
exports.X_REQUEST_ID = 'request_id';
exports.SESSION_TOKEN = 'x-bce-security-token';
exports.X_VOD_MEDIA_TITLE = 'x-vod-media-title';
exports.X_VOD_MEDIA_DESCRIPTION = 'x-vod-media-description';
exports.ACCEPT_ENCODING = 'accept-encoding';
exports.ACCEPT = 'accept';
//# sourceMappingURL=data:application/json;base64,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