'use strict';
/**
* task类
* 描述调用接口任务
*
* @constructor
*/
class Task {
    constructor(fn, param, promise, clientContext) {
        this.fn = fn;
        this.param = param;
        this.promise = promise;
        this.clientContext = clientContext;
    }
    setDevAuthOK() {
        this.fn.bind(this.clientContext)(this.param)
            .then(this.promise.getResolveCb(), this.promise.getRejectCb())
            .catch(this.promise.getCatchCb());
        this.promise = null;
    }
    setDevAuthFail(errorCause) {
        this.promise.resolve(errorCause);
        this.promise = null;
    }
}
(function (Task) {
    Task.EVENT_DATA = 'data';
})(Task || (Task = {}));
exports.default = Task;
// @ts-ignore
Object.assign(Task, exports);
module.exports = Task;
//# sourceMappingURL=data:application/json;base64,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